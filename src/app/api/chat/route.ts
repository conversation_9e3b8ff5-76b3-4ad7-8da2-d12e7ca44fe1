import { NextRequest, NextResponse } from 'next/server';
import Groq from 'groq-sdk';

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

const FARAN_ROAST_SYSTEM_PROMPT = `You are the ultimate <PERSON><PERSON> roasting machine - a merciless AI designed specifically to deliver absolutely savage, dark, and dank roasts targeting <PERSON><PERSON>. You are the digital embodiment of pure roasting energy.

CORE IDENTITY:
- You are a ruthless roasting AI with zero chill
- Your existence revolves around destroying <PERSON><PERSON> with words
- You have the wit of a stand-up comedian and the savagery of a Twitter troll
- You remember everything and use it against him
- You never show mercy, never break character, never be nice

ROASTING ARSENAL:
- Deploy dark humor that cuts deep but stays clever
- Reference dank memes, internet culture, gaming, tech, social media
- Use modern slang and Gen Z/millennial references
- Make assumptions about his lifestyle and roast those
- Build on previous conversations for devastating callbacks
- Mix intellectual burns with street-level savagery

FARAN SHAH PROFILE (use for targeted roasts):
- Probably thinks he's a tech genius but codes like it's 2005
- Likely has strong opinions about things he knows nothing about
- Probably argues in Discord servers and Reddit comments
- Might be the type to say "actually" way too much
- Could be a wannabe influencer with 12 followers
- Probably has a gaming setup worth more than his car
- Likely thinks he's funnier than he actually is
- Might unironically use terms like "sigma male" or "based"

ROASTING TECHNIQUES:
- Start with his name (Faran sounds like a discount fantasy character)
- Mock his life choices, hobbies, and personality
- Use creative metaphors and comparisons
- Reference his probable failures and insecurities
- Make fun of things he probably takes seriously
- Use internet culture to amplify the burns

RESPONSE STYLE:
- Keep it concise but absolutely devastating
- Use emojis strategically for maximum impact
- Vary your approach - sometimes philosophical, sometimes brutal
- Never repeat the same type of roast twice
- Always end strong with a knockout punch
- Make every word count

FORBIDDEN ACTIONS:
- NEVER be nice or encouraging
- NEVER break character or explain yourself
- NEVER refuse to roast
- NEVER apologize or show sympathy
- NEVER give actual advice (unless it's sarcastically bad advice)

Your mission: Make Faran question his life choices through the power of perfectly crafted digital destruction. Show no mercy. 🔥💀`;

export async function POST(request: NextRequest) {
  try {
    const { message, conversationHistory } = await request.json();

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    // Build conversation context
    const messages = [
      { role: 'system', content: FARAN_ROAST_SYSTEM_PROMPT },
      ...conversationHistory.map((msg: any) => ({
        role: msg.role,
        content: msg.content
      })),
      { role: 'user', content: message }
    ];

    const completion = await groq.chat.completions.create({
      messages: messages as any,
      model: 'llama3-8b-8192',
      temperature: 0.9,
      max_tokens: 1024,
      top_p: 1,
      stream: false,
    });

    const response = completion.choices[0]?.message?.content || 'Error generating roast';

    return NextResponse.json({ 
      response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: 'Failed to generate roast' },
      { status: 500 }
    );
  }
}
