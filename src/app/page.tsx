'use client';

import { useState, useRef, useEffect } from 'react';
import Message from '@/components/Message';
import ChatInput from '@/components/ChatInput';
import TypingIndicator from '@/components/TypingIndicator';

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
}

export default function Home() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingWelcome, setIsGeneratingWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const generateWelcomeMessage = async () => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: "Generate a brutal welcome message for when <PERSON><PERSON> first opens this chat. Make it personal, dark, and absolutely savage. This is the first thing he'll see.",
          conversationHistory: [],
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate welcome message');
      }

      const data = await response.json();

      const welcomeMessage: ChatMessage = {
        id: '1',
        content: data.response,
        role: 'assistant',
        timestamp: data.timestamp,
      };

      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('Error generating welcome message:', error);
      // Fallback welcome message
      const fallbackMessage: ChatMessage = {
        id: '1',
        content: "Oh look, it's Faran Shah... the human equivalent of a participation trophy. 💀 Ready to get absolutely destroyed? Because I've been waiting specifically for YOU. Say something and watch me turn your entire existence into a meme. 🔥🤡💀",
        role: 'assistant',
        timestamp: new Date().toISOString(),
      };
      setMessages([fallbackMessage]);
    } finally {
      setIsGeneratingWelcome(false);
    }
  };

  useEffect(() => {
    generateWelcomeMessage();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  const sendMessage = async (content: string) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content,
      role: 'user',
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      const conversationHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          conversationHistory,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const data = await response.json();

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: data.response,
        role: 'assistant',
        timestamp: data.timestamp,
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: "Even my error messages are better than your comebacks, Faran. Try again! 💀",
        role: 'assistant',
        timestamp: new Date().toISOString(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 p-6 border-b border-gray-700/50 shadow-lg">
        <h1 className="text-3xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-orange-500 fire-text">
          🔥 FARAN ROAST BOT 🔥
        </h1>
        <p className="text-center text-gray-300 text-sm mt-2 font-medium">
          Psychological Warfare Division - Faran Shah Elimination Protocol
        </p>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-2 bg-gradient-to-b from-transparent to-gray-900/20">
        {isGeneratingWelcome ? (
          <TypingIndicator />
        ) : (
          <>
            {messages.map((message) => (
              <Message
                key={message.id}
                content={message.content}
                isUser={message.role === 'user'}
                timestamp={message.timestamp}
              />
            ))}
            {isLoading && <TypingIndicator />}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <ChatInput onSendMessage={sendMessage} isLoading={isLoading} />
    </div>
  );
}
