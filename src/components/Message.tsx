interface MessageProps {
  content: string;
  isUser: boolean;
  timestamp: string;
}

export default function Message({ content, isUser, timestamp }: MessageProps) {
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
        isUser 
          ? 'bg-blue-600 text-white' 
          : 'bg-red-600 text-white shadow-lg border border-red-500'
      }`}>
        <p className="text-sm">{content}</p>
        <p className={`text-xs mt-1 ${
          isUser ? 'text-blue-200' : 'text-red-200'
        }`}>
          {new Date(timestamp).toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
}
