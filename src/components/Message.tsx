import { useEffect, useState } from 'react';

interface MessageProps {
  content: string;
  isUser: boolean;
  timestamp: string;
}

export default function Message({ content, isUser, timestamp }: MessageProps) {
  const [formattedTime, setFormattedTime] = useState('');

  useEffect(() => {
    // Format time on client side to avoid hydration mismatch
    setFormattedTime(new Date(timestamp).toLocaleTimeString());
  }, [timestamp]);

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-6 message-enter`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl transition-all duration-300 hover:scale-105 ${
        isUser
          ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg shadow-blue-500/25'
          : 'bg-gradient-to-r from-red-600 to-red-700 text-white shadow-xl shadow-red-500/40 border border-red-500/50 roast-glow'
      }`}>
        <p className="text-sm leading-relaxed">{content}</p>
        <p className={`text-xs mt-2 ${
          isUser ? 'text-blue-200' : 'text-red-200'
        }`}>
          {formattedTime}
        </p>
      </div>
    </div>
  );
}
