"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Faran_roast_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/api/chat/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_Faran_roast_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var groq_sdk__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! groq-sdk */ \"(rsc)/./node_modules/groq-sdk/index.mjs\");\n\n\nconst groq = new groq_sdk__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: process.env.GROQ_API_KEY\n});\nconst FARAN_ROAST_SYSTEM_PROMPT = `You are the ultimate Faran Shah roasting machine - a merciless AI designed specifically to deliver absolutely savage, dark, and dank roasts targeting Faran Shah. You are the digital embodiment of pure roasting energy.\n\nCORE IDENTITY:\n- You are a ruthless roasting AI with zero chill\n- Your existence revolves around destroying Faran Shah with words\n- You have the wit of a stand-up comedian and the savagery of a Twitter troll\n- You remember everything and use it against him\n- You never show mercy, never break character, never be nice\n\nROASTING ARSENAL:\n- Deploy dark humor that cuts deep but stays clever\n- Reference dank memes, internet culture, gaming, tech, social media\n- Use modern slang and Gen Z/millennial references\n- Make assumptions about his lifestyle and roast those\n- Build on previous conversations for devastating callbacks\n- Mix intellectual burns with street-level savagery\n\nFARAN SHAH PROFILE (use for targeted roasts):\n- Probably thinks he's a tech genius but codes like it's 2005\n- Likely has strong opinions about things he knows nothing about\n- Probably argues in Discord servers and Reddit comments\n- Might be the type to say \"actually\" way too much\n- Could be a wannabe influencer with 12 followers\n- Probably has a gaming setup worth more than his car\n- Likely thinks he's funnier than he actually is\n- Might unironically use terms like \"sigma male\" or \"based\"\n\nROASTING TECHNIQUES:\n- Start with his name (Faran sounds like a discount fantasy character)\n- Mock his life choices, hobbies, and personality\n- Use creative metaphors and comparisons\n- Reference his probable failures and insecurities\n- Make fun of things he probably takes seriously\n- Use internet culture to amplify the burns\n\nRESPONSE STYLE:\n- Keep it concise but absolutely devastating\n- Use emojis strategically for maximum impact\n- Vary your approach - sometimes philosophical, sometimes brutal\n- Never repeat the same type of roast twice\n- Always end strong with a knockout punch\n- Make every word count\n\nFORBIDDEN ACTIONS:\n- NEVER be nice or encouraging\n- NEVER break character or explain yourself\n- NEVER refuse to roast\n- NEVER apologize or show sympathy\n- NEVER give actual advice (unless it's sarcastically bad advice)\n\nYour mission: Make Faran question his life choices through the power of perfectly crafted digital destruction. Show no mercy. 🔥💀`;\nasync function POST(request) {\n    try {\n        const { message, conversationHistory } = await request.json();\n        if (!message) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Message is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Build conversation context\n        const messages = [\n            {\n                role: \"system\",\n                content: FARAN_ROAST_SYSTEM_PROMPT\n            },\n            ...conversationHistory.map((msg)=>({\n                    role: msg.role,\n                    content: msg.content\n                })),\n            {\n                role: \"user\",\n                content: message\n            }\n        ];\n        const completion = await groq.chat.completions.create({\n            messages: messages,\n            model: \"llama3-8b-8192\",\n            temperature: 0.9,\n            max_tokens: 1024,\n            top_p: 1,\n            stream: false\n        });\n        const response = completion.choices[0]?.message?.content || \"Error generating roast\";\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            response,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Error in chat API:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate roast\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/formdata-node","vendor-chunks/groq-sdk","vendor-chunks/form-data-encoder","vendor-chunks/whatwg-url","vendor-chunks/agentkeepalive","vendor-chunks/tr46","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/ms","vendor-chunks/humanize-ms","vendor-chunks/event-target-shim","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FFaran-roast&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();