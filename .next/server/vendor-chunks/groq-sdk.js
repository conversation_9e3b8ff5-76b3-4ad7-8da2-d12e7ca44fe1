"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/groq-sdk";
exports.ids = ["vendor-chunks/groq-sdk"];
exports.modules = {

/***/ "(rsc)/./node_modules/groq-sdk/_shims/MultipartBody.mjs":
/*!********************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/MultipartBody.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultipartBody: () => (/* binding */ MultipartBody)\n/* harmony export */ });\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */ class MultipartBody {\n    constructor(body){\n        this.body = body;\n    }\n    get [Symbol.toStringTag]() {\n        return \"MultipartBody\";\n    }\n} //# sourceMappingURL=MultipartBody.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL011bHRpcGFydEJvZHkubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUNNLE1BQU1BO0lBQ1RDLFlBQVlDLElBQUksQ0FBRTtRQUNkLElBQUksQ0FBQ0EsSUFBSSxHQUFHQTtJQUNoQjtJQUNBLElBQUksQ0FBQ0MsT0FBT0MsV0FBVyxDQUFDLEdBQUc7UUFDdkIsT0FBTztJQUNYO0FBQ0osRUFDQSwwQ0FBMEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYXJhbi1yb2FzdC1ib3QvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL011bHRpcGFydEJvZHkubWpzP2YzYjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5leHBvcnQgY2xhc3MgTXVsdGlwYXJ0Qm9keSB7XG4gICAgY29uc3RydWN0b3IoYm9keSkge1xuICAgICAgICB0aGlzLmJvZHkgPSBib2R5O1xuICAgIH1cbiAgICBnZXQgW1N5bWJvbC50b1N0cmluZ1RhZ10oKSB7XG4gICAgICAgIHJldHVybiAnTXVsdGlwYXJ0Qm9keSc7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9TXVsdGlwYXJ0Qm9keS5tanMubWFwIl0sIm5hbWVzIjpbIk11bHRpcGFydEJvZHkiLCJjb25zdHJ1Y3RvciIsImJvZHkiLCJTeW1ib2wiLCJ0b1N0cmluZ1RhZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/MultipartBody.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/_shims/index.mjs":
/*!************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/index.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Blob),\n/* harmony export */   File: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.File),\n/* harmony export */   FormData: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData),\n/* harmony export */   Headers: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Headers),\n/* harmony export */   ReadableStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.ReadableStream),\n/* harmony export */   Request: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Request),\n/* harmony export */   Response: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Response),\n/* harmony export */   auto: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.auto),\n/* harmony export */   fetch: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions),\n/* harmony export */   isFsReadStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream),\n/* harmony export */   kind: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind),\n/* harmony export */   setShims: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims)\n/* harmony export */ });\n/* harmony import */ var _registry_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/registry.mjs\");\n/* harmony import */ var groq_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! groq-sdk/_shims/auto/runtime */ \"(rsc)/./node_modules/groq-sdk/_shims/node-runtime.mjs\");\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */ \n\nif (!_registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind) _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims(groq_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__.getRuntime(), {\n    auto: true\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOztDQUVDLEdBQ3VDO0FBQ2E7QUFDckQsSUFBSSxDQUFDQSwrQ0FBVSxFQUFFQSxtREFBYyxDQUFDQyxtRUFBZSxJQUFJO0lBQUVBLE1BQU07QUFBSztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhcmFuLXJvYXN0LWJvdC8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9fc2hpbXMvaW5kZXgubWpzPzcwNjIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5pbXBvcnQgKiBhcyBzaGltcyBmcm9tICcuL3JlZ2lzdHJ5Lm1qcyc7XG5pbXBvcnQgKiBhcyBhdXRvIGZyb20gJ2dyb3Etc2RrL19zaGltcy9hdXRvL3J1bnRpbWUnO1xuaWYgKCFzaGltcy5raW5kKSBzaGltcy5zZXRTaGltcyhhdXRvLmdldFJ1bnRpbWUoKSwgeyBhdXRvOiB0cnVlIH0pO1xuZXhwb3J0ICogZnJvbSAnLi9yZWdpc3RyeS5tanMnO1xuIl0sIm5hbWVzIjpbInNoaW1zIiwiYXV0byIsImtpbmQiLCJzZXRTaGltcyIsImdldFJ1bnRpbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/_shims/node-runtime.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/node-runtime.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntime: () => (/* binding */ getRuntime)\n/* harmony export */ });\n/* harmony import */ var node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\");\n/* harmony import */ var formdata_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formdata-node */ \"(rsc)/./node_modules/formdata-node/lib/esm/index.js\");\n/* harmony import */ var agentkeepalive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! agentkeepalive */ \"(rsc)/./node_modules/agentkeepalive/index.js\");\n/* harmony import */ var abort_controller__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! abort-controller */ \"(rsc)/./node_modules/abort-controller/dist/abort-controller.js\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var form_data_encoder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! form-data-encoder */ \"(rsc)/./node_modules/form-data-encoder/lib/esm/index.js\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MultipartBody.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/MultipartBody.mjs\");\n/* harmony import */ var node_stream_web__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! node:stream/web */ \"node:stream/web\");\n\n\n\n\n\n\n\n\n\nlet fileFromPathWarned = false;\nasync function fileFromPath(path, ...args) {\n    // this import fails in environments that don't handle export maps correctly, like old versions of Jest\n    const { fileFromPath: _fileFromPath } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/formdata-node\"), __webpack_require__.e(\"vendor-chunks/node-domexception\")]).then(__webpack_require__.bind(__webpack_require__, /*! formdata-node/file-from-path */ \"(rsc)/./node_modules/formdata-node/lib/esm/fileFromPath.js\"));\n    if (!fileFromPathWarned) {\n        console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(path)}) instead`);\n        fileFromPathWarned = true;\n    }\n    // @ts-ignore\n    return await _fileFromPath(path, ...args);\n}\nconst defaultHttpAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__({\n    keepAlive: true,\n    timeout: 5 * 60 * 1000\n});\nconst defaultHttpsAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__.HttpsAgent({\n    keepAlive: true,\n    timeout: 5 * 60 * 1000\n});\nasync function getMultipartRequestOptions(form, opts) {\n    const encoder = new form_data_encoder__WEBPACK_IMPORTED_MODULE_5__.FormDataEncoder(form);\n    const readable = node_stream__WEBPACK_IMPORTED_MODULE_6__.Readable.from(encoder);\n    const body = new _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__.MultipartBody(readable);\n    const headers = {\n        ...opts.headers,\n        ...encoder.headers,\n        \"Content-Length\": encoder.contentLength\n    };\n    return {\n        ...opts,\n        body: body,\n        headers\n    };\n}\nfunction getRuntime() {\n    // Polyfill global object if needed.\n    if (typeof AbortController === \"undefined\") {\n        // @ts-expect-error (the types are subtly different, but compatible in practice)\n        globalThis.AbortController = abort_controller__WEBPACK_IMPORTED_MODULE_3__.AbortController;\n    }\n    return {\n        kind: \"node\",\n        fetch: node_fetch__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        Request: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Request,\n        Response: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Response,\n        Headers: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers,\n        FormData: formdata_node__WEBPACK_IMPORTED_MODULE_1__.FormData,\n        Blob: formdata_node__WEBPACK_IMPORTED_MODULE_1__.Blob,\n        File: formdata_node__WEBPACK_IMPORTED_MODULE_1__.File,\n        ReadableStream: node_stream_web__WEBPACK_IMPORTED_MODULE_7__.ReadableStream,\n        getMultipartRequestOptions,\n        getDefaultAgent: (url)=>url.startsWith(\"https\") ? defaultHttpsAgent : defaultHttpAgent,\n        fileFromPath,\n        isFsReadStream: (value)=>value instanceof node_fs__WEBPACK_IMPORTED_MODULE_4__.ReadStream\n    };\n} //# sourceMappingURL=node-runtime.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/node-runtime.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/_shims/registry.mjs":
/*!***************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/registry.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   FormData: () => (/* binding */ FormData),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   ReadableStream: () => (/* binding */ ReadableStream),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   Response: () => (/* binding */ Response),\n/* harmony export */   auto: () => (/* binding */ auto),\n/* harmony export */   fetch: () => (/* binding */ fetch),\n/* harmony export */   fileFromPath: () => (/* binding */ fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* binding */ getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* binding */ getMultipartRequestOptions),\n/* harmony export */   isFsReadStream: () => (/* binding */ isFsReadStream),\n/* harmony export */   kind: () => (/* binding */ kind),\n/* harmony export */   setShims: () => (/* binding */ setShims)\n/* harmony export */ });\nlet auto = false;\nlet kind = undefined;\nlet fetch = undefined;\nlet Request = undefined;\nlet Response = undefined;\nlet Headers = undefined;\nlet FormData = undefined;\nlet Blob = undefined;\nlet File = undefined;\nlet ReadableStream = undefined;\nlet getMultipartRequestOptions = undefined;\nlet getDefaultAgent = undefined;\nlet fileFromPath = undefined;\nlet isFsReadStream = undefined;\nfunction setShims(shims, options = {\n    auto: false\n}) {\n    if (auto) {\n        throw new Error(`you must \\`import 'groq-sdk/shims/${shims.kind}'\\` before importing anything else from groq-sdk`);\n    }\n    if (kind) {\n        throw new Error(`can't \\`import 'groq-sdk/shims/${shims.kind}'\\` after \\`import 'groq-sdk/shims/${kind}'\\``);\n    }\n    auto = options.auto;\n    kind = shims.kind;\n    fetch = shims.fetch;\n    Request = shims.Request;\n    Response = shims.Response;\n    Headers = shims.Headers;\n    FormData = shims.FormData;\n    Blob = shims.Blob;\n    File = shims.File;\n    ReadableStream = shims.ReadableStream;\n    getMultipartRequestOptions = shims.getMultipartRequestOptions;\n    getDefaultAgent = shims.getDefaultAgent;\n    fileFromPath = shims.fileFromPath;\n    isFsReadStream = shims.isFsReadStream;\n} //# sourceMappingURL=registry.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/registry.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/core.mjs":
/*!****************************************!*\
  !*** ./node_modules/groq-sdk/core.mjs ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIClient: () => (/* binding */ APIClient),\n/* harmony export */   APIPromise: () => (/* binding */ APIPromise),\n/* harmony export */   AbstractPage: () => (/* binding */ AbstractPage),\n/* harmony export */   PagePromise: () => (/* binding */ PagePromise),\n/* harmony export */   castToError: () => (/* binding */ castToError),\n/* harmony export */   coerceBoolean: () => (/* binding */ coerceBoolean),\n/* harmony export */   coerceFloat: () => (/* binding */ coerceFloat),\n/* harmony export */   coerceInteger: () => (/* binding */ coerceInteger),\n/* harmony export */   createForm: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.createForm),\n/* harmony export */   createResponseHeaders: () => (/* binding */ createResponseHeaders),\n/* harmony export */   debug: () => (/* binding */ debug),\n/* harmony export */   ensurePresent: () => (/* binding */ ensurePresent),\n/* harmony export */   getRequiredHeader: () => (/* binding */ getRequiredHeader),\n/* harmony export */   hasOwn: () => (/* binding */ hasOwn),\n/* harmony export */   isEmptyObj: () => (/* binding */ isEmptyObj),\n/* harmony export */   isHeadersProtocol: () => (/* binding */ isHeadersProtocol),\n/* harmony export */   isObj: () => (/* binding */ isObj),\n/* harmony export */   isRequestOptions: () => (/* binding */ isRequestOptions),\n/* harmony export */   isRunningInBrowser: () => (/* binding */ isRunningInBrowser),\n/* harmony export */   maybeCoerceBoolean: () => (/* binding */ maybeCoerceBoolean),\n/* harmony export */   maybeCoerceFloat: () => (/* binding */ maybeCoerceFloat),\n/* harmony export */   maybeCoerceInteger: () => (/* binding */ maybeCoerceInteger),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions),\n/* harmony export */   readEnv: () => (/* binding */ readEnv),\n/* harmony export */   safeJSON: () => (/* binding */ safeJSON),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   toBase64: () => (/* binding */ toBase64)\n/* harmony export */ });\n/* harmony import */ var _version_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.mjs */ \"(rsc)/./node_modules/groq-sdk/version.mjs\");\n/* harmony import */ var _lib_streaming_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/streaming.mjs */ \"(rsc)/./node_modules/groq-sdk/lib/streaming.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./error.mjs */ \"(rsc)/./node_modules/groq-sdk/error.mjs\");\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uploads.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\nvar __classPrivateFieldSet = undefined && undefined.__classPrivateFieldSet || function(receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar __classPrivateFieldGet = undefined && undefined.__classPrivateFieldGet || function(receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _AbstractPage_client;\n\n\n\n\n\n\nasync function defaultParseResponse(props) {\n    const { response } = props;\n    if (props.options.stream) {\n        debug(\"response\", response.status, response.url, response.headers, response.body);\n        // Note: there is an invariant here that isn't represented in the type system\n        // that if you set `stream: true` the response type must also be `Stream<T>`\n        if (props.options.__streamClass) {\n            return props.options.__streamClass.fromSSEResponse(response, props.controller);\n        }\n        return _lib_streaming_mjs__WEBPACK_IMPORTED_MODULE_2__.Stream.fromSSEResponse(response, props.controller);\n    }\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n        return null;\n    }\n    if (props.options.__binaryResponse) {\n        return response;\n    }\n    const contentType = response.headers.get(\"content-type\");\n    const isJSON = contentType?.includes(\"application/json\") || contentType?.includes(\"application/vnd.api+json\");\n    if (isJSON) {\n        const json = await response.json();\n        debug(\"response\", response.status, response.url, response.headers, json);\n        return json;\n    }\n    const text = await response.text();\n    debug(\"response\", response.status, response.url, response.headers, text);\n    // TODO handle blob, arraybuffer, other content types, etc.\n    return text;\n}\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */ class APIPromise extends Promise {\n    constructor(responsePromise, parseResponse = defaultParseResponse){\n        super((resolve)=>{\n            // this is maybe a bit weird but this has to be a no-op to not implicitly\n            // parse the response body; instead .then, .catch, .finally are overridden\n            // to parse the response\n            resolve(null);\n        });\n        this.responsePromise = responsePromise;\n        this.parseResponse = parseResponse;\n    }\n    _thenUnwrap(transform) {\n        return new APIPromise(this.responsePromise, async (props)=>transform(await this.parseResponse(props)));\n    }\n    /**\n     * Gets the raw `Response` instance instead of parsing the response\n     * data.\n     *\n     * If you want to parse the response body but still get the `Response`\n     * instance, you can use {@link withResponse()}.\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */ asResponse() {\n        return this.responsePromise.then((p)=>p.response);\n    }\n    /**\n     * Gets the parsed response data and the raw `Response` instance.\n     *\n     * If you just want to get the raw `Response` instance without parsing it,\n     * you can use {@link asResponse()}.\n     *\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */ async withResponse() {\n        const [data, response] = await Promise.all([\n            this.parse(),\n            this.asResponse()\n        ]);\n        return {\n            data,\n            response\n        };\n    }\n    parse() {\n        if (!this.parsedPromise) {\n            this.parsedPromise = this.responsePromise.then(this.parseResponse);\n        }\n        return this.parsedPromise;\n    }\n    then(onfulfilled, onrejected) {\n        return this.parse().then(onfulfilled, onrejected);\n    }\n    catch(onrejected) {\n        return this.parse().catch(onrejected);\n    }\n    finally(onfinally) {\n        return this.parse().finally(onfinally);\n    }\n}\nclass APIClient {\n    constructor({ baseURL, maxRetries = 2, timeout = 60000, httpAgent, fetch: overridenFetch }){\n        this.baseURL = baseURL;\n        this.maxRetries = validatePositiveInteger(\"maxRetries\", maxRetries);\n        this.timeout = validatePositiveInteger(\"timeout\", timeout);\n        this.httpAgent = httpAgent;\n        this.fetch = overridenFetch ?? _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch;\n    }\n    authHeaders(opts) {\n        return {};\n    }\n    /**\n     * Override this to add your own default headers, for example:\n     *\n     *  {\n     *    ...super.defaultHeaders(),\n     *    Authorization: 'Bearer 123',\n     *  }\n     */ defaultHeaders(opts) {\n        return {\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\",\n            \"User-Agent\": this.getUserAgent(),\n            ...getPlatformHeaders(),\n            ...this.authHeaders(opts)\n        };\n    }\n    /**\n     * Override this to add your own headers validation:\n     */ validateHeaders(headers, customHeaders) {}\n    defaultIdempotencyKey() {\n        return `stainless-node-retry-${uuid4()}`;\n    }\n    get(path, opts) {\n        return this.methodRequest(\"get\", path, opts);\n    }\n    post(path, opts) {\n        return this.methodRequest(\"post\", path, opts);\n    }\n    patch(path, opts) {\n        return this.methodRequest(\"patch\", path, opts);\n    }\n    put(path, opts) {\n        return this.methodRequest(\"put\", path, opts);\n    }\n    delete(path, opts) {\n        return this.methodRequest(\"delete\", path, opts);\n    }\n    methodRequest(method, path, opts) {\n        return this.request(Promise.resolve(opts).then(async (opts)=>{\n            const body = opts && (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isBlobLike)(opts?.body) ? new DataView(await opts.body.arrayBuffer()) : opts?.body instanceof DataView ? opts.body : opts?.body instanceof ArrayBuffer ? new DataView(opts.body) : opts && ArrayBuffer.isView(opts?.body) ? new DataView(opts.body.buffer) : opts?.body;\n            return {\n                method,\n                path,\n                ...opts,\n                body\n            };\n        }));\n    }\n    getAPIList(path, Page, opts) {\n        return this.requestAPIList(Page, {\n            method: \"get\",\n            path,\n            ...opts\n        });\n    }\n    calculateContentLength(body) {\n        if (typeof body === \"string\") {\n            if (typeof Buffer !== \"undefined\") {\n                return Buffer.byteLength(body, \"utf8\").toString();\n            }\n            if (typeof TextEncoder !== \"undefined\") {\n                const encoder = new TextEncoder();\n                const encoded = encoder.encode(body);\n                return encoded.length.toString();\n            }\n        } else if (ArrayBuffer.isView(body)) {\n            return body.byteLength.toString();\n        }\n        return null;\n    }\n    buildRequest(options) {\n        const { method, path, query, headers: headers = {} } = options;\n        const body = ArrayBuffer.isView(options.body) || options.__binaryRequest && typeof options.body === \"string\" ? options.body : (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) ? options.body.body : options.body ? JSON.stringify(options.body, null, 2) : null;\n        const contentLength = this.calculateContentLength(body);\n        const url = this.buildURL(path, query);\n        if (\"timeout\" in options) validatePositiveInteger(\"timeout\", options.timeout);\n        const timeout = options.timeout ?? this.timeout;\n        const httpAgent = options.httpAgent ?? this.httpAgent ?? (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent)(url);\n        const minAgentTimeout = timeout + 1000;\n        if (typeof httpAgent?.options?.timeout === \"number\" && minAgentTimeout > (httpAgent.options.timeout ?? 0)) {\n            // Allow any given request to bump our agent active socket timeout.\n            // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n            // and without mutating agent we would need to create more of them.\n            // This tradeoff optimizes for performance.\n            httpAgent.options.timeout = minAgentTimeout;\n        }\n        if (this.idempotencyHeader && method !== \"get\") {\n            if (!options.idempotencyKey) options.idempotencyKey = this.defaultIdempotencyKey();\n            headers[this.idempotencyHeader] = options.idempotencyKey;\n        }\n        const reqHeaders = this.buildHeaders({\n            options,\n            headers,\n            contentLength\n        });\n        const req = {\n            method,\n            ...body && {\n                body: body\n            },\n            headers: reqHeaders,\n            ...httpAgent && {\n                agent: httpAgent\n            },\n            // @ts-ignore node-fetch uses a custom AbortSignal type that is\n            // not compatible with standard web types\n            signal: options.signal ?? null\n        };\n        return {\n            req,\n            url,\n            timeout\n        };\n    }\n    buildHeaders({ options, headers, contentLength }) {\n        const reqHeaders = {};\n        if (contentLength) {\n            reqHeaders[\"content-length\"] = contentLength;\n        }\n        const defaultHeaders = this.defaultHeaders(options);\n        applyHeadersMut(reqHeaders, defaultHeaders);\n        applyHeadersMut(reqHeaders, headers);\n        // let builtin fetch set the Content-Type for multipart bodies\n        if ((0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) && _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.kind !== \"node\") {\n            delete reqHeaders[\"content-type\"];\n        }\n        this.validateHeaders(reqHeaders, headers);\n        return reqHeaders;\n    }\n    /**\n     * Used as a callback for mutating the given `FinalRequestOptions` object.\n     */ async prepareOptions(options) {}\n    /**\n     * Used as a callback for mutating the given `RequestInit` object.\n     *\n     * This is useful for cases where you want to add certain headers based off of\n     * the request properties, e.g. `method` or `url`.\n     */ async prepareRequest(request, { url, options }) {}\n    parseHeaders(headers) {\n        return !headers ? {} : Symbol.iterator in headers ? Object.fromEntries(Array.from(headers).map((header)=>[\n                ...header\n            ])) : {\n            ...headers\n        };\n    }\n    makeStatusError(status, error, message, headers) {\n        return _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIError.generate(status, error, message, headers);\n    }\n    request(options, remainingRetries = null) {\n        return new APIPromise(this.makeRequest(options, remainingRetries));\n    }\n    async makeRequest(optionsInput, retriesRemaining) {\n        const options = await optionsInput;\n        if (retriesRemaining == null) {\n            retriesRemaining = options.maxRetries ?? this.maxRetries;\n        }\n        await this.prepareOptions(options);\n        const { req, url, timeout } = this.buildRequest(options);\n        await this.prepareRequest(req, {\n            url,\n            options\n        });\n        debug(\"request\", url, options, req.headers);\n        if (options.signal?.aborted) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIUserAbortError();\n        }\n        const controller = new AbortController();\n        const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);\n        if (response instanceof Error) {\n            if (options.signal?.aborted) {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIUserAbortError();\n            }\n            if (retriesRemaining) {\n                return this.retryRequest(options, retriesRemaining);\n            }\n            if (response.name === \"AbortError\") {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIConnectionTimeoutError();\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIConnectionError({\n                cause: response\n            });\n        }\n        const responseHeaders = createResponseHeaders(response.headers);\n        if (!response.ok) {\n            if (retriesRemaining && this.shouldRetry(response)) {\n                const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n                debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n                return this.retryRequest(options, retriesRemaining, responseHeaders);\n            }\n            const errText = await response.text().catch((e)=>castToError(e).message);\n            const errJSON = safeJSON(errText);\n            const errMessage = errJSON ? undefined : errText;\n            const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n            debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n            const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n            throw err;\n        }\n        return {\n            response,\n            options,\n            controller\n        };\n    }\n    requestAPIList(Page, options) {\n        const request = this.makeRequest(options, null);\n        return new PagePromise(this, request, Page);\n    }\n    buildURL(path, query) {\n        const url = isAbsoluteURL(path) ? new URL(path) : new URL(this.baseURL + (this.baseURL.endsWith(\"/\") && path.startsWith(\"/\") ? path.slice(1) : path));\n        const defaultQuery = this.defaultQuery();\n        if (!isEmptyObj(defaultQuery)) {\n            query = {\n                ...defaultQuery,\n                ...query\n            };\n        }\n        if (typeof query === \"object\" && query && !Array.isArray(query)) {\n            url.search = this.stringifyQuery(query);\n        }\n        return url.toString();\n    }\n    stringifyQuery(query) {\n        return Object.entries(query).filter(([_, value])=>typeof value !== \"undefined\").map(([key, value])=>{\n            if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n            }\n            if (value === null) {\n                return `${encodeURIComponent(key)}=`;\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);\n        }).join(\"&\");\n    }\n    async fetchWithTimeout(url, init, ms, controller) {\n        const { signal, ...options } = init || {};\n        if (signal) signal.addEventListener(\"abort\", ()=>controller.abort());\n        const timeout = setTimeout(()=>controller.abort(), ms);\n        return this.getRequestClient()// use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n        .fetch.call(undefined, url, {\n            signal: controller.signal,\n            ...options\n        }).finally(()=>{\n            clearTimeout(timeout);\n        });\n    }\n    getRequestClient() {\n        return {\n            fetch: this.fetch\n        };\n    }\n    shouldRetry(response) {\n        // Note this is not a standard header.\n        const shouldRetryHeader = response.headers.get(\"x-should-retry\");\n        // If the server explicitly says whether or not to retry, obey.\n        if (shouldRetryHeader === \"true\") return true;\n        if (shouldRetryHeader === \"false\") return false;\n        // Retry on request timeouts.\n        if (response.status === 408) return true;\n        // Retry on lock timeouts.\n        if (response.status === 409) return true;\n        // Retry on rate limits.\n        if (response.status === 429) return true;\n        // Retry internal errors.\n        if (response.status >= 500) return true;\n        return false;\n    }\n    async retryRequest(options, retriesRemaining, responseHeaders) {\n        let timeoutMillis;\n        // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n        const retryAfterMillisHeader = responseHeaders?.[\"retry-after-ms\"];\n        if (retryAfterMillisHeader) {\n            const timeoutMs = parseFloat(retryAfterMillisHeader);\n            if (!Number.isNaN(timeoutMs)) {\n                timeoutMillis = timeoutMs;\n            }\n        }\n        // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n        const retryAfterHeader = responseHeaders?.[\"retry-after\"];\n        if (retryAfterHeader && !timeoutMillis) {\n            const timeoutSeconds = parseFloat(retryAfterHeader);\n            if (!Number.isNaN(timeoutSeconds)) {\n                timeoutMillis = timeoutSeconds * 1000;\n            } else {\n                timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n            }\n        }\n        // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n        // just do what it says, but otherwise calculate a default\n        if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n            const maxRetries = options.maxRetries ?? this.maxRetries;\n            timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n        }\n        await sleep(timeoutMillis);\n        return this.makeRequest(options, retriesRemaining - 1);\n    }\n    calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {\n        const initialRetryDelay = 0.5;\n        const maxRetryDelay = 8.0;\n        const numRetries = maxRetries - retriesRemaining;\n        // Apply exponential backoff, but not more than the max.\n        const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n        // Apply some jitter, take up to at most 25 percent of the retry time.\n        const jitter = 1 - Math.random() * 0.25;\n        return sleepSeconds * jitter * 1000;\n    }\n    getUserAgent() {\n        return `${this.constructor.name}/JS ${_version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION}`;\n    }\n}\nclass AbstractPage {\n    constructor(client, response, body, options){\n        _AbstractPage_client.set(this, void 0);\n        __classPrivateFieldSet(this, _AbstractPage_client, client, \"f\");\n        this.options = options;\n        this.response = response;\n        this.body = body;\n    }\n    hasNextPage() {\n        const items = this.getPaginatedItems();\n        if (!items.length) return false;\n        return this.nextPageInfo() != null;\n    }\n    async getNextPage() {\n        const nextInfo = this.nextPageInfo();\n        if (!nextInfo) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(\"No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.\");\n        }\n        const nextOptions = {\n            ...this.options\n        };\n        if (\"params\" in nextInfo && typeof nextOptions.query === \"object\") {\n            nextOptions.query = {\n                ...nextOptions.query,\n                ...nextInfo.params\n            };\n        } else if (\"url\" in nextInfo) {\n            const params = [\n                ...Object.entries(nextOptions.query || {}),\n                ...nextInfo.url.searchParams.entries()\n            ];\n            for (const [key, value] of params){\n                nextInfo.url.searchParams.set(key, value);\n            }\n            nextOptions.query = undefined;\n            nextOptions.path = nextInfo.url.toString();\n        }\n        return await __classPrivateFieldGet(this, _AbstractPage_client, \"f\").requestAPIList(this.constructor, nextOptions);\n    }\n    async *iterPages() {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let page = this;\n        yield page;\n        while(page.hasNextPage()){\n            page = await page.getNextPage();\n            yield page;\n        }\n    }\n    async *[(_AbstractPage_client = new WeakMap(), Symbol.asyncIterator)]() {\n        for await (const page of this.iterPages()){\n            for (const item of page.getPaginatedItems()){\n                yield item;\n            }\n        }\n    }\n}\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */ class PagePromise extends APIPromise {\n    constructor(client, request, Page){\n        super(request, async (props)=>new Page(client, props.response, await defaultParseResponse(props), props.options));\n    }\n    /**\n     * Allow auto-paginating iteration on an unawaited list call, eg:\n     *\n     *    for await (const item of client.items.list()) {\n     *      console.log(item)\n     *    }\n     */ async *[Symbol.asyncIterator]() {\n        const page = await this;\n        for await (const item of page){\n            yield item;\n        }\n    }\n}\nconst createResponseHeaders = (headers)=>{\n    return new Proxy(Object.fromEntries(// @ts-ignore\n    headers.entries()), {\n        get (target, name) {\n            const key = name.toString();\n            return target[key.toLowerCase()] || target[key];\n        }\n    });\n};\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys = {\n    method: true,\n    path: true,\n    query: true,\n    body: true,\n    headers: true,\n    maxRetries: true,\n    stream: true,\n    timeout: true,\n    httpAgent: true,\n    signal: true,\n    idempotencyKey: true,\n    __binaryRequest: true,\n    __binaryResponse: true,\n    __streamClass: true\n};\nconst isRequestOptions = (obj)=>{\n    return typeof obj === \"object\" && obj !== null && !isEmptyObj(obj) && Object.keys(obj).every((k)=>hasOwn(requestOptionsKeys, k));\n};\nconst getPlatformProperties = ()=>{\n    if (typeof Deno !== \"undefined\" && Deno.build != null) {\n        return {\n            \"X-Stainless-Lang\": \"js\",\n            \"X-Stainless-Package-Version\": _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            \"X-Stainless-OS\": normalizePlatform(Deno.build.os),\n            \"X-Stainless-Arch\": normalizeArch(Deno.build.arch),\n            \"X-Stainless-Runtime\": \"deno\",\n            \"X-Stainless-Runtime-Version\": typeof Deno.version === \"string\" ? Deno.version : Deno.version?.deno ?? \"unknown\"\n        };\n    }\n    if (typeof EdgeRuntime !== \"undefined\") {\n        return {\n            \"X-Stainless-Lang\": \"js\",\n            \"X-Stainless-Package-Version\": _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            \"X-Stainless-OS\": \"Unknown\",\n            \"X-Stainless-Arch\": `other:${EdgeRuntime}`,\n            \"X-Stainless-Runtime\": \"edge\",\n            \"X-Stainless-Runtime-Version\": process.version\n        };\n    }\n    // Check if Node.js\n    if (Object.prototype.toString.call(typeof process !== \"undefined\" ? process : 0) === \"[object process]\") {\n        return {\n            \"X-Stainless-Lang\": \"js\",\n            \"X-Stainless-Package-Version\": _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            \"X-Stainless-OS\": normalizePlatform(process.platform),\n            \"X-Stainless-Arch\": normalizeArch(process.arch),\n            \"X-Stainless-Runtime\": \"node\",\n            \"X-Stainless-Runtime-Version\": process.version\n        };\n    }\n    const browserInfo = getBrowserInfo();\n    if (browserInfo) {\n        return {\n            \"X-Stainless-Lang\": \"js\",\n            \"X-Stainless-Package-Version\": _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            \"X-Stainless-OS\": \"Unknown\",\n            \"X-Stainless-Arch\": \"unknown\",\n            \"X-Stainless-Runtime\": `browser:${browserInfo.browser}`,\n            \"X-Stainless-Runtime-Version\": browserInfo.version\n        };\n    }\n    // TODO add support for Cloudflare workers, etc.\n    return {\n        \"X-Stainless-Lang\": \"js\",\n        \"X-Stainless-Package-Version\": _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n        \"X-Stainless-OS\": \"Unknown\",\n        \"X-Stainless-Arch\": \"unknown\",\n        \"X-Stainless-Runtime\": \"unknown\",\n        \"X-Stainless-Runtime-Version\": \"unknown\"\n    };\n};\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo() {\n    if (typeof navigator === \"undefined\" || !navigator) {\n        return null;\n    }\n    // NOTE: The order matters here!\n    const browserPatterns = [\n        {\n            key: \"edge\",\n            pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/\n        },\n        {\n            key: \"ie\",\n            pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/\n        },\n        {\n            key: \"ie\",\n            pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/\n        },\n        {\n            key: \"chrome\",\n            pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/\n        },\n        {\n            key: \"firefox\",\n            pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/\n        },\n        {\n            key: \"safari\",\n            pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/\n        }\n    ];\n    // Find the FIRST matching browser\n    for (const { key, pattern } of browserPatterns){\n        const match = pattern.exec(navigator.userAgent);\n        if (match) {\n            const major = match[1] || 0;\n            const minor = match[2] || 0;\n            const patch = match[3] || 0;\n            return {\n                browser: key,\n                version: `${major}.${minor}.${patch}`\n            };\n        }\n    }\n    return null;\n}\nconst normalizeArch = (arch)=>{\n    // Node docs:\n    // - https://nodejs.org/api/process.html#processarch\n    // Deno docs:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    if (arch === \"x32\") return \"x32\";\n    if (arch === \"x86_64\" || arch === \"x64\") return \"x64\";\n    if (arch === \"arm\") return \"arm\";\n    if (arch === \"aarch64\" || arch === \"arm64\") return \"arm64\";\n    if (arch) return `other:${arch}`;\n    return \"unknown\";\n};\nconst normalizePlatform = (platform)=>{\n    // Node platforms:\n    // - https://nodejs.org/api/process.html#processplatform\n    // Deno platforms:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    // - https://github.com/denoland/deno/issues/14799\n    platform = platform.toLowerCase();\n    // NOTE: this iOS check is untested and may not work\n    // Node does not work natively on IOS, there is a fork at\n    // https://github.com/nodejs-mobile/nodejs-mobile\n    // however it is unknown at the time of writing how to detect if it is running\n    if (platform.includes(\"ios\")) return \"iOS\";\n    if (platform === \"android\") return \"Android\";\n    if (platform === \"darwin\") return \"MacOS\";\n    if (platform === \"win32\") return \"Windows\";\n    if (platform === \"freebsd\") return \"FreeBSD\";\n    if (platform === \"openbsd\") return \"OpenBSD\";\n    if (platform === \"linux\") return \"Linux\";\n    if (platform) return `Other:${platform}`;\n    return \"Unknown\";\n};\nlet _platformHeaders;\nconst getPlatformHeaders = ()=>{\n    return _platformHeaders ?? (_platformHeaders = getPlatformProperties());\n};\nconst safeJSON = (text)=>{\n    try {\n        return JSON.parse(text);\n    } catch (err) {\n        return undefined;\n    }\n};\n// https://stackoverflow.com/a/19709846\nconst startsWithSchemeRegexp = new RegExp(\"^(?:[a-z]+:)?//\", \"i\");\nconst isAbsoluteURL = (url)=>{\n    return startsWithSchemeRegexp.test(url);\n};\nconst sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nconst validatePositiveInteger = (name, n)=>{\n    if (typeof n !== \"number\" || !Number.isInteger(n)) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`${name} must be an integer`);\n    }\n    if (n < 0) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`${name} must be a positive integer`);\n    }\n    return n;\n};\nconst castToError = (err)=>{\n    if (err instanceof Error) return err;\n    return new Error(err);\n};\nconst ensurePresent = (value)=>{\n    if (value == null) throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Expected a value to be given but received ${value} instead.`);\n    return value;\n};\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */ const readEnv = (env)=>{\n    if (typeof process !== \"undefined\") {\n        return process.env?.[env]?.trim() ?? undefined;\n    }\n    if (typeof Deno !== \"undefined\") {\n        return Deno.env?.get?.(env)?.trim();\n    }\n    return undefined;\n};\nconst coerceInteger = (value)=>{\n    if (typeof value === \"number\") return Math.round(value);\n    if (typeof value === \"string\") return parseInt(value, 10);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceFloat = (value)=>{\n    if (typeof value === \"number\") return value;\n    if (typeof value === \"string\") return parseFloat(value);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceBoolean = (value)=>{\n    if (typeof value === \"boolean\") return value;\n    if (typeof value === \"string\") return value === \"true\";\n    return Boolean(value);\n};\nconst maybeCoerceInteger = (value)=>{\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceInteger(value);\n};\nconst maybeCoerceFloat = (value)=>{\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceFloat(value);\n};\nconst maybeCoerceBoolean = (value)=>{\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceBoolean(value);\n};\n// https://stackoverflow.com/a/34491287\nfunction isEmptyObj(obj) {\n    if (!obj) return true;\n    for(const _k in obj)return false;\n    return true;\n}\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nfunction hasOwn(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */ function applyHeadersMut(targetHeaders, newHeaders) {\n    for(const k in newHeaders){\n        if (!hasOwn(newHeaders, k)) continue;\n        const lowerKey = k.toLowerCase();\n        if (!lowerKey) continue;\n        const val = newHeaders[k];\n        if (val === null) {\n            delete targetHeaders[lowerKey];\n        } else if (val !== undefined) {\n            targetHeaders[lowerKey] = val;\n        }\n    }\n}\nfunction debug(action, ...args) {\n    if (typeof process !== \"undefined\" && process?.env?.[\"DEBUG\"] === \"true\") {\n        console.log(`Groq:DEBUG:${action}`, ...args);\n    }\n}\n/**\n * https://stackoverflow.com/a/2117523\n */ const uuid4 = ()=>{\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = Math.random() * 16 | 0;\n        const v = c === \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n};\nconst isRunningInBrowser = ()=>{\n    return(// @ts-ignore\n     false && // @ts-ignore\n    0);\n};\nconst isHeadersProtocol = (headers)=>{\n    return typeof headers?.get === \"function\";\n};\nconst getRequiredHeader = (headers, header)=>{\n    const lowerCasedHeader = header.toLowerCase();\n    if (isHeadersProtocol(headers)) {\n        // to deal with the case where the header looks like Stainless-Event-Id\n        const intercapsHeader = header[0]?.toUpperCase() + header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2)=>g1 + g2.toUpperCase());\n        for (const key of [\n            header,\n            lowerCasedHeader,\n            header.toUpperCase(),\n            intercapsHeader\n        ]){\n            const value = headers.get(key);\n            if (value) {\n                return value;\n            }\n        }\n    }\n    for (const [key, value] of Object.entries(headers)){\n        if (key.toLowerCase() === lowerCasedHeader) {\n            if (Array.isArray(value)) {\n                if (value.length <= 1) return value[0];\n                console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n                return value[0];\n            }\n            return value;\n        }\n    }\n    throw new Error(`Could not find ${header} header`);\n};\n/**\n * Encodes a string to Base64 format.\n */ const toBase64 = (str)=>{\n    if (!str) return \"\";\n    if (typeof Buffer !== \"undefined\") {\n        return Buffer.from(str).toString(\"base64\");\n    }\n    if (typeof btoa !== \"undefined\") {\n        return btoa(str);\n    }\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(\"Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined\");\n};\nfunction isObj(obj) {\n    return obj != null && typeof obj === \"object\" && !Array.isArray(obj);\n} //# sourceMappingURL=core.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/core.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/error.mjs":
/*!*****************************************!*\
  !*** ./node_modules/groq-sdk/error.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* binding */ APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* binding */ APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   APIUserAbortError: () => (/* binding */ APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* binding */ AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   ConflictError: () => (/* binding */ ConflictError),\n/* harmony export */   GroqError: () => (/* binding */ GroqError),\n/* harmony export */   InternalServerError: () => (/* binding */ InternalServerError),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* binding */ PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* binding */ RateLimitError),\n/* harmony export */   UnprocessableEntityError: () => (/* binding */ UnprocessableEntityError)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(rsc)/./node_modules/groq-sdk/core.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass GroqError extends Error {\n}\nclass APIError extends GroqError {\n    constructor(status, error, message, headers){\n        super(`${APIError.makeMessage(status, error, message)}`);\n        this.status = status;\n        this.headers = headers;\n        this.error = error;\n    }\n    static makeMessage(status, error, message) {\n        const msg = error?.message ? typeof error.message === \"string\" ? error.message : JSON.stringify(error.message) : error ? JSON.stringify(error) : message;\n        if (status && msg) {\n            return `${status} ${msg}`;\n        }\n        if (status) {\n            return `${status} status code (no body)`;\n        }\n        if (msg) {\n            return msg;\n        }\n        return \"(no status code or body)\";\n    }\n    static generate(status, errorResponse, message, headers) {\n        if (!status) {\n            return new APIConnectionError({\n                cause: (0,_core_mjs__WEBPACK_IMPORTED_MODULE_0__.castToError)(errorResponse)\n            });\n        }\n        const error = errorResponse;\n        if (status === 400) {\n            return new BadRequestError(status, error, message, headers);\n        }\n        if (status === 401) {\n            return new AuthenticationError(status, error, message, headers);\n        }\n        if (status === 403) {\n            return new PermissionDeniedError(status, error, message, headers);\n        }\n        if (status === 404) {\n            return new NotFoundError(status, error, message, headers);\n        }\n        if (status === 409) {\n            return new ConflictError(status, error, message, headers);\n        }\n        if (status === 422) {\n            return new UnprocessableEntityError(status, error, message, headers);\n        }\n        if (status === 429) {\n            return new RateLimitError(status, error, message, headers);\n        }\n        if (status >= 500) {\n            return new InternalServerError(status, error, message, headers);\n        }\n        return new APIError(status, error, message, headers);\n    }\n}\nclass APIUserAbortError extends APIError {\n    constructor({ message } = {}){\n        super(undefined, undefined, message || \"Request was aborted.\", undefined);\n        this.status = undefined;\n    }\n}\nclass APIConnectionError extends APIError {\n    constructor({ message, cause }){\n        super(undefined, undefined, message || \"Connection error.\", undefined);\n        this.status = undefined;\n        // in some environments the 'cause' property is already declared\n        // @ts-ignore\n        if (cause) this.cause = cause;\n    }\n}\nclass APIConnectionTimeoutError extends APIConnectionError {\n    constructor({ message } = {}){\n        super({\n            message: message ?? \"Request timed out.\"\n        });\n    }\n}\nclass BadRequestError extends APIError {\n    constructor(){\n        super(...arguments);\n        this.status = 400;\n    }\n}\nclass AuthenticationError extends APIError {\n    constructor(){\n        super(...arguments);\n        this.status = 401;\n    }\n}\nclass PermissionDeniedError extends APIError {\n    constructor(){\n        super(...arguments);\n        this.status = 403;\n    }\n}\nclass NotFoundError extends APIError {\n    constructor(){\n        super(...arguments);\n        this.status = 404;\n    }\n}\nclass ConflictError extends APIError {\n    constructor(){\n        super(...arguments);\n        this.status = 409;\n    }\n}\nclass UnprocessableEntityError extends APIError {\n    constructor(){\n        super(...arguments);\n        this.status = 422;\n    }\n}\nclass RateLimitError extends APIError {\n    constructor(){\n        super(...arguments);\n        this.status = 429;\n    }\n}\nclass InternalServerError extends APIError {\n} //# sourceMappingURL=error.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/error.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/index.mjs":
/*!*****************************************!*\
  !*** ./node_modules/groq-sdk/index.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* binding */ APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* binding */ APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   APIUserAbortError: () => (/* binding */ APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* binding */ AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   ConflictError: () => (/* binding */ ConflictError),\n/* harmony export */   Groq: () => (/* binding */ Groq),\n/* harmony export */   GroqError: () => (/* binding */ GroqError),\n/* harmony export */   InternalServerError: () => (/* binding */ InternalServerError),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* binding */ PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* binding */ RateLimitError),\n/* harmony export */   UnprocessableEntityError: () => (/* binding */ UnprocessableEntityError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fileFromPath: () => (/* binding */ fileFromPath),\n/* harmony export */   toFile: () => (/* binding */ toFile)\n/* harmony export */ });\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error.mjs */ \"(rsc)/./node_modules/groq-sdk/error.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./uploads.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./uploads.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(rsc)/./node_modules/groq-sdk/core.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resources/index.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/completions.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resources/index.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/chat/chat.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./resources/index.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/embeddings.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./resources/index.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/audio/audio.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./resources/index.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/models.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar _a;\n\n\n\n\n/**\n * API Client for interfacing with the Groq API.\n */ class Groq extends _core_mjs__WEBPACK_IMPORTED_MODULE_0__.APIClient {\n    /**\n     * API Client for interfacing with the Groq API.\n     *\n     * @param {string | undefined} [opts.apiKey=process.env['GROQ_API_KEY'] ?? undefined]\n     * @param {string} [opts.baseURL=process.env['GROQ_BASE_URL'] ?? https://api.groq.com] - Override the default base URL for the API.\n     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n     * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n     */ constructor({ baseURL = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv(\"GROQ_BASE_URL\"), apiKey = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv(\"GROQ_API_KEY\"), ...opts } = {}){\n        if (apiKey === undefined) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(\"The GROQ_API_KEY environment variable is missing or empty; either provide it, or instantiate the Groq client with an apiKey option, like new Groq({ apiKey: 'My API Key' }).\");\n        }\n        const options = {\n            apiKey,\n            ...opts,\n            baseURL: baseURL || `https://api.groq.com`\n        };\n        if (!options.dangerouslyAllowBrowser && _core_mjs__WEBPACK_IMPORTED_MODULE_0__.isRunningInBrowser()) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(\"It looks like you're running in a browser-like environment.\\n\\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\\nIf you understand the risks and have appropriate mitigations in place,\\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\\n\\nnew Groq({ apiKey, dangerouslyAllowBrowser: true })\");\n        }\n        super({\n            baseURL: options.baseURL,\n            timeout: options.timeout ?? 60000 /* 1 minute */ ,\n            httpAgent: options.httpAgent,\n            maxRetries: options.maxRetries,\n            fetch: options.fetch\n        });\n        this.completions = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Completions(this);\n        this.chat = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__.Chat(this);\n        this.embeddings = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__.Embeddings(this);\n        this.audio = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__.Audio(this);\n        this.models = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__.Models(this);\n        this._options = options;\n        this.apiKey = apiKey;\n    }\n    defaultQuery() {\n        return this._options.defaultQuery;\n    }\n    defaultHeaders(opts) {\n        return {\n            ...super.defaultHeaders(opts),\n            ...this._options.defaultHeaders\n        };\n    }\n    authHeaders(opts) {\n        return {\n            Authorization: `Bearer ${this.apiKey}`\n        };\n    }\n}\n_a = Groq;\nGroq.Groq = _a;\nGroq.DEFAULT_TIMEOUT = 60000; // 1 minute\nGroq.GroqError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError;\nGroq.APIError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError;\nGroq.APIConnectionError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionError;\nGroq.APIConnectionTimeoutError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionTimeoutError;\nGroq.APIUserAbortError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIUserAbortError;\nGroq.NotFoundError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.NotFoundError;\nGroq.ConflictError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.ConflictError;\nGroq.RateLimitError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.RateLimitError;\nGroq.BadRequestError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.BadRequestError;\nGroq.AuthenticationError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.AuthenticationError;\nGroq.InternalServerError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.InternalServerError;\nGroq.PermissionDeniedError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.PermissionDeniedError;\nGroq.UnprocessableEntityError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.UnprocessableEntityError;\nGroq.toFile = _uploads_mjs__WEBPACK_IMPORTED_MODULE_7__.toFile;\nGroq.fileFromPath = _uploads_mjs__WEBPACK_IMPORTED_MODULE_8__.fileFromPath;\nconst { GroqError, APIError, APIConnectionError, APIConnectionTimeoutError, APIUserAbortError, NotFoundError, ConflictError, RateLimitError, BadRequestError, AuthenticationError, InternalServerError, PermissionDeniedError, UnprocessableEntityError } = _error_mjs__WEBPACK_IMPORTED_MODULE_1__;\nvar toFile = _uploads_mjs__WEBPACK_IMPORTED_MODULE_7__.toFile;\nvar fileFromPath = _uploads_mjs__WEBPACK_IMPORTED_MODULE_8__.fileFromPath;\n(function(Groq) {\n    Groq.Completions = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Completions;\n    Groq.Chat = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__.Chat;\n    Groq.Embeddings = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__.Embeddings;\n    Groq.Audio = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__.Audio;\n    Groq.Models = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__.Models;\n})(Groq || (Groq = {}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Groq); //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/lib/streaming.mjs":
/*!*************************************************!*\
  !*** ./node_modules/groq-sdk/lib/streaming.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Stream: () => (/* binding */ Stream),\n/* harmony export */   readableStreamAsyncIterable: () => (/* binding */ readableStreamAsyncIterable)\n/* harmony export */ });\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_shims/index.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../error.mjs */ \"(rsc)/./node_modules/groq-sdk/error.mjs\");\n\n\n\nclass Stream {\n    constructor(iterator, controller){\n        this.iterator = iterator;\n        this.controller = controller;\n    }\n    static fromSSEResponse(response, controller) {\n        let consumed = false;\n        const decoder = new SSEDecoder();\n        async function* iterMessages() {\n            if (!response.body) {\n                controller.abort();\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Attempted to iterate over a response with no body`);\n            }\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(response.body);\n            for await (const chunk of iter){\n                for (const line of lineDecoder.decode(chunk)){\n                    const sse = decoder.decode(line);\n                    if (sse) yield sse;\n                }\n            }\n            for (const line of lineDecoder.flush()){\n                const sse = decoder.decode(line);\n                if (sse) yield sse;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error(\"Cannot iterate over a consumed stream, use `.tee()` to split the stream.\");\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const sse of iterMessages()){\n                    if (done) continue;\n                    if (sse.data.startsWith(\"[DONE]\")) {\n                        done = true;\n                        continue;\n                    }\n                    if (sse.event === null || sse.event === \"error\") {\n                        let data;\n                        try {\n                            data = JSON.parse(sse.data);\n                        } catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                        if (data && data.error) {\n                            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError(data.error.status_code, data.error, data.error.message, undefined);\n                        }\n                        yield data;\n                    }\n                }\n                done = true;\n            } catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === \"AbortError\") return;\n                throw e;\n            } finally{\n                // If the user `break`s, abort the ongoing request.\n                if (!done) controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    /**\n     * Generates a Stream from a newline-separated ReadableStream\n     * where each item is a JSON value.\n     */ static fromReadableStream(readableStream, controller) {\n        let consumed = false;\n        async function* iterLines() {\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(readableStream);\n            for await (const chunk of iter){\n                for (const line of lineDecoder.decode(chunk)){\n                    yield line;\n                }\n            }\n            for (const line of lineDecoder.flush()){\n                yield line;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error(\"Cannot iterate over a consumed stream, use `.tee()` to split the stream.\");\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const line of iterLines()){\n                    if (done) continue;\n                    if (line) yield JSON.parse(line);\n                }\n                done = true;\n            } catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === \"AbortError\") return;\n                throw e;\n            } finally{\n                // If the user `break`s, abort the ongoing request.\n                if (!done) controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    [Symbol.asyncIterator]() {\n        return this.iterator();\n    }\n    /**\n     * Splits the stream into two streams which can be\n     * independently read from at different speeds.\n     */ tee() {\n        const left = [];\n        const right = [];\n        const iterator = this.iterator();\n        const teeIterator = (queue)=>{\n            return {\n                next: ()=>{\n                    if (queue.length === 0) {\n                        const result = iterator.next();\n                        left.push(result);\n                        right.push(result);\n                    }\n                    return queue.shift();\n                }\n            };\n        };\n        return [\n            new Stream(()=>teeIterator(left), this.controller),\n            new Stream(()=>teeIterator(right), this.controller)\n        ];\n    }\n    /**\n     * Converts this stream to a newline-separated ReadableStream of\n     * JSON stringified values in the stream\n     * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n     */ toReadableStream() {\n        const self = this;\n        let iter;\n        const encoder = new TextEncoder();\n        return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.ReadableStream({\n            async start () {\n                iter = self[Symbol.asyncIterator]();\n            },\n            async pull (ctrl) {\n                try {\n                    const { value, done } = await iter.next();\n                    if (done) return ctrl.close();\n                    const bytes = encoder.encode(JSON.stringify(value) + \"\\n\");\n                    ctrl.enqueue(bytes);\n                } catch (err) {\n                    ctrl.error(err);\n                }\n            },\n            async cancel () {\n                await iter.return?.();\n            }\n        });\n    }\n}\nclass SSEDecoder {\n    constructor(){\n        this.event = null;\n        this.data = [];\n        this.chunks = [];\n    }\n    decode(line) {\n        if (line.endsWith(\"\\r\")) {\n            line = line.substring(0, line.length - 1);\n        }\n        if (!line) {\n            // empty line and we didn't previously encounter any messages\n            if (!this.event && !this.data.length) return null;\n            const sse = {\n                event: this.event,\n                data: this.data.join(\"\\n\"),\n                raw: this.chunks\n            };\n            this.event = null;\n            this.data = [];\n            this.chunks = [];\n            return sse;\n        }\n        this.chunks.push(line);\n        if (line.startsWith(\":\")) {\n            return null;\n        }\n        let [fieldname, _, value] = partition(line, \":\");\n        if (value.startsWith(\" \")) {\n            value = value.substring(1);\n        }\n        if (fieldname === \"event\") {\n            this.event = value;\n        } else if (fieldname === \"data\") {\n            this.data.push(value);\n        }\n        return null;\n    }\n}\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */ class LineDecoder {\n    constructor(){\n        this.buffer = [];\n        this.trailingCR = false;\n    }\n    decode(chunk) {\n        let text = this.decodeText(chunk);\n        if (this.trailingCR) {\n            text = \"\\r\" + text;\n            this.trailingCR = false;\n        }\n        if (text.endsWith(\"\\r\")) {\n            this.trailingCR = true;\n            text = text.slice(0, -1);\n        }\n        if (!text) {\n            return [];\n        }\n        const trailingNewline = LineDecoder.NEWLINE_CHARS.has(text[text.length - 1] || \"\");\n        let lines = text.split(LineDecoder.NEWLINE_REGEXP);\n        if (lines.length === 1 && !trailingNewline) {\n            this.buffer.push(lines[0]);\n            return [];\n        }\n        if (this.buffer.length > 0) {\n            lines = [\n                this.buffer.join(\"\") + lines[0],\n                ...lines.slice(1)\n            ];\n            this.buffer = [];\n        }\n        if (!trailingNewline) {\n            this.buffer = [\n                lines.pop() || \"\"\n            ];\n        }\n        return lines;\n    }\n    decodeText(bytes) {\n        if (bytes == null) return \"\";\n        if (typeof bytes === \"string\") return bytes;\n        // Node:\n        if (typeof Buffer !== \"undefined\") {\n            if (bytes instanceof Buffer) {\n                return bytes.toString();\n            }\n            if (bytes instanceof Uint8Array) {\n                return Buffer.from(bytes).toString();\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: received non-Uint8Array (${bytes.constructor.name}) stream chunk in an environment with a global \"Buffer\" defined, which this library assumes to be Node. Please report this error.`);\n        }\n        // Browser\n        if (typeof TextDecoder !== \"undefined\") {\n            if (bytes instanceof Uint8Array || bytes instanceof ArrayBuffer) {\n                this.textDecoder ?? (this.textDecoder = new TextDecoder(\"utf8\"));\n                return this.textDecoder.decode(bytes);\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: received non-Uint8Array/ArrayBuffer (${bytes.constructor.name}) in a web platform. Please report this error.`);\n        }\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.`);\n    }\n    flush() {\n        if (!this.buffer.length && !this.trailingCR) {\n            return [];\n        }\n        const lines = [\n            this.buffer.join(\"\")\n        ];\n        this.buffer = [];\n        this.trailingCR = false;\n        return lines;\n    }\n}\n// prettier-ignore\nLineDecoder.NEWLINE_CHARS = new Set([\n    \"\\n\",\n    \"\\r\",\n    \"\\v\",\n    \"\\f\",\n    \"\\x1c\",\n    \"\\x1d\",\n    \"\\x1e\",\n    \"\\x85\",\n    \"\\u2028\",\n    \"\\u2029\"\n]);\nLineDecoder.NEWLINE_REGEXP = /\\r\\n|[\\n\\r\\x0b\\x0c\\x1c\\x1d\\x1e\\x85\\u2028\\u2029]/g;\nfunction partition(str, delimiter) {\n    const index = str.indexOf(delimiter);\n    if (index !== -1) {\n        return [\n            str.substring(0, index),\n            delimiter,\n            str.substring(index + delimiter.length)\n        ];\n    }\n    return [\n        str,\n        \"\",\n        \"\"\n    ];\n}\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */ function readableStreamAsyncIterable(stream) {\n    if (stream[Symbol.asyncIterator]) return stream;\n    const reader = stream.getReader();\n    return {\n        async next () {\n            try {\n                const result = await reader.read();\n                if (result?.done) reader.releaseLock(); // release lock when stream becomes closed\n                return result;\n            } catch (e) {\n                reader.releaseLock(); // release lock when stream becomes errored\n                throw e;\n            }\n        },\n        async return () {\n            const cancelPromise = reader.cancel();\n            reader.releaseLock();\n            await cancelPromise;\n            return {\n                done: true,\n                value: undefined\n            };\n        },\n        [Symbol.asyncIterator] () {\n            return this;\n        }\n    };\n} //# sourceMappingURL=streaming.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/lib/streaming.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resource.mjs":
/*!********************************************!*\
  !*** ./node_modules/groq-sdk/resource.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIResource: () => (/* binding */ APIResource)\n/* harmony export */ });\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nclass APIResource {\n    constructor(client){\n        this._client = client;\n    }\n} //# sourceMappingURL=resource.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxzRkFBc0Y7QUFDL0UsTUFBTUE7SUFDVEMsWUFBWUMsTUFBTSxDQUFFO1FBQ2hCLElBQUksQ0FBQ0MsT0FBTyxHQUFHRDtJQUNuQjtBQUNKLEVBQ0EscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmFyYW4tcm9hc3QtYm90Ly4vbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlLm1qcz84YmZlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5leHBvcnQgY2xhc3MgQVBJUmVzb3VyY2Uge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLl9jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVzb3VyY2UubWpzLm1hcCJdLCJuYW1lcyI6WyJBUElSZXNvdXJjZSIsImNvbnN0cnVjdG9yIiwiY2xpZW50IiwiX2NsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resource.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/audio/audio.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/audio.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Audio: () => (/* binding */ Audio)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./transcriptions.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs\");\n/* harmony import */ var _translations_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./translations.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/audio/translations.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\nclass Audio extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor(){\n        super(...arguments);\n        this.transcriptions = new _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_1__.Transcriptions(this._client);\n        this.translations = new _translations_mjs__WEBPACK_IMPORTED_MODULE_2__.Translations(this._client);\n    }\n}\n(function(Audio) {\n    Audio.Transcriptions = _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_1__.Transcriptions;\n    Audio.Translations = _translations_mjs__WEBPACK_IMPORTED_MODULE_2__.Translations;\n})(Audio || (Audio = {})); //# sourceMappingURL=audio.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL2F1ZGlvLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsc0ZBQXNGO0FBQ3JDO0FBQ1M7QUFDSjtBQUMvQyxNQUFNRyxjQUFjSCxzREFBV0E7SUFDbENJLGFBQWM7UUFDVixLQUFLLElBQUlDO1FBQ1QsSUFBSSxDQUFDQyxjQUFjLEdBQUcsSUFBSUwsK0RBQWdDLENBQUMsSUFBSSxDQUFDTyxPQUFPO1FBQ3ZFLElBQUksQ0FBQ0MsWUFBWSxHQUFHLElBQUlQLDJEQUE0QixDQUFDLElBQUksQ0FBQ00sT0FBTztJQUNyRTtBQUNKO0FBQ0MsVUFBVUwsS0FBSztJQUNaQSxNQUFNSSxjQUFjLEdBQUdOLCtEQUFnQztJQUN2REUsTUFBTU8sWUFBWSxHQUFHUiwyREFBNEI7QUFDckQsR0FBR0MsU0FBVUEsQ0FBQUEsUUFBUSxDQUFDLEtBQ3RCLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhcmFuLXJvYXN0LWJvdC8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvYXVkaW8vYXVkaW8ubWpzP2I1ZjMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgVHJhbnNjcmlwdGlvbnNBUEkgZnJvbSBcIi4vdHJhbnNjcmlwdGlvbnMubWpzXCI7XG5pbXBvcnQgKiBhcyBUcmFuc2xhdGlvbnNBUEkgZnJvbSBcIi4vdHJhbnNsYXRpb25zLm1qc1wiO1xuZXhwb3J0IGNsYXNzIEF1ZGlvIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICB0aGlzLnRyYW5zY3JpcHRpb25zID0gbmV3IFRyYW5zY3JpcHRpb25zQVBJLlRyYW5zY3JpcHRpb25zKHRoaXMuX2NsaWVudCk7XG4gICAgICAgIHRoaXMudHJhbnNsYXRpb25zID0gbmV3IFRyYW5zbGF0aW9uc0FQSS5UcmFuc2xhdGlvbnModGhpcy5fY2xpZW50KTtcbiAgICB9XG59XG4oZnVuY3Rpb24gKEF1ZGlvKSB7XG4gICAgQXVkaW8uVHJhbnNjcmlwdGlvbnMgPSBUcmFuc2NyaXB0aW9uc0FQSS5UcmFuc2NyaXB0aW9ucztcbiAgICBBdWRpby5UcmFuc2xhdGlvbnMgPSBUcmFuc2xhdGlvbnNBUEkuVHJhbnNsYXRpb25zO1xufSkoQXVkaW8gfHwgKEF1ZGlvID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWF1ZGlvLm1qcy5tYXAiXSwibmFtZXMiOlsiQVBJUmVzb3VyY2UiLCJUcmFuc2NyaXB0aW9uc0FQSSIsIlRyYW5zbGF0aW9uc0FQSSIsIkF1ZGlvIiwiY29uc3RydWN0b3IiLCJhcmd1bWVudHMiLCJ0cmFuc2NyaXB0aW9ucyIsIlRyYW5zY3JpcHRpb25zIiwiX2NsaWVudCIsInRyYW5zbGF0aW9ucyIsIlRyYW5zbGF0aW9ucyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/audio/audio.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/transcriptions.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Transcriptions: () => (/* binding */ Transcriptions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Transcriptions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Transcribes audio into the input language.\n     */ create(body, options) {\n        return this._client.post(\"/openai/v1/audio/transcriptions\", _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({\n            body,\n            ...options\n        }));\n    }\n}\n(function(Transcriptions) {})(Transcriptions || (Transcriptions = {})); //# sourceMappingURL=transcriptions.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3RyYW5zY3JpcHRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxzRkFBc0Y7QUFDckM7QUFDVjtBQUNoQyxNQUFNRSx1QkFBdUJGLHNEQUFXQTtJQUMzQzs7S0FFQyxHQUNERyxPQUFPQyxJQUFJLEVBQUVDLE9BQU8sRUFBRTtRQUNsQixPQUFPLElBQUksQ0FBQ0MsT0FBTyxDQUFDQyxJQUFJLENBQUMsbUNBQW1DTixrRUFBZ0MsQ0FBQztZQUFFRztZQUFNLEdBQUdDLE9BQU87UUFBQztJQUNwSDtBQUNKO0FBQ0MsVUFBVUgsY0FBYyxHQUN6QixHQUFHQSxrQkFBbUJBLENBQUFBLGlCQUFpQixDQUFDLEtBQ3hDLDJDQUEyQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhcmFuLXJvYXN0LWJvdC8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvYXVkaW8vdHJhbnNjcmlwdGlvbnMubWpzPzgxMTQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgQ29yZSBmcm9tIFwiLi4vLi4vY29yZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBUcmFuc2NyaXB0aW9ucyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBUcmFuc2NyaWJlcyBhdWRpbyBpbnRvIHRoZSBpbnB1dCBsYW5ndWFnZS5cbiAgICAgKi9cbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy9vcGVuYWkvdjEvYXVkaW8vdHJhbnNjcmlwdGlvbnMnLCBDb3JlLm11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucyh7IGJvZHksIC4uLm9wdGlvbnMgfSkpO1xuICAgIH1cbn1cbihmdW5jdGlvbiAoVHJhbnNjcmlwdGlvbnMpIHtcbn0pKFRyYW5zY3JpcHRpb25zIHx8IChUcmFuc2NyaXB0aW9ucyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cmFuc2NyaXB0aW9ucy5tanMubWFwIl0sIm5hbWVzIjpbIkFQSVJlc291cmNlIiwiQ29yZSIsIlRyYW5zY3JpcHRpb25zIiwiY3JlYXRlIiwiYm9keSIsIm9wdGlvbnMiLCJfY2xpZW50IiwicG9zdCIsIm11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/audio/translations.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/translations.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translations: () => (/* binding */ Translations)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Translations extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Translates audio into English.\n     */ create(body, options) {\n        return this._client.post(\"/openai/v1/audio/translations\", _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({\n            body,\n            ...options\n        }));\n    }\n}\n(function(Translations) {})(Translations || (Translations = {})); //# sourceMappingURL=translations.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3RyYW5zbGF0aW9ucy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsc0ZBQXNGO0FBQ3JDO0FBQ1Y7QUFDaEMsTUFBTUUscUJBQXFCRixzREFBV0E7SUFDekM7O0tBRUMsR0FDREcsT0FBT0MsSUFBSSxFQUFFQyxPQUFPLEVBQUU7UUFDbEIsT0FBTyxJQUFJLENBQUNDLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLGlDQUFpQ04sa0VBQWdDLENBQUM7WUFBRUc7WUFBTSxHQUFHQyxPQUFPO1FBQUM7SUFDbEg7QUFDSjtBQUNDLFVBQVVILFlBQVksR0FDdkIsR0FBR0EsZ0JBQWlCQSxDQUFBQSxlQUFlLENBQUMsS0FDcEMseUNBQXlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmFyYW4tcm9hc3QtYm90Ly4vbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9hdWRpby90cmFuc2xhdGlvbnMubWpzP2I2YzUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgQ29yZSBmcm9tIFwiLi4vLi4vY29yZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBUcmFuc2xhdGlvbnMgZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogVHJhbnNsYXRlcyBhdWRpbyBpbnRvIEVuZ2xpc2guXG4gICAgICovXG4gICAgY3JlYXRlKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KCcvb3BlbmFpL3YxL2F1ZGlvL3RyYW5zbGF0aW9ucycsIENvcmUubXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zKHsgYm9keSwgLi4ub3B0aW9ucyB9KSk7XG4gICAgfVxufVxuKGZ1bmN0aW9uIChUcmFuc2xhdGlvbnMpIHtcbn0pKFRyYW5zbGF0aW9ucyB8fCAoVHJhbnNsYXRpb25zID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYW5zbGF0aW9ucy5tanMubWFwIl0sIm5hbWVzIjpbIkFQSVJlc291cmNlIiwiQ29yZSIsIlRyYW5zbGF0aW9ucyIsImNyZWF0ZSIsImJvZHkiLCJvcHRpb25zIiwiX2NsaWVudCIsInBvc3QiLCJtdWx0aXBhcnRGb3JtUmVxdWVzdE9wdGlvbnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/audio/translations.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/chat/chat.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/groq-sdk/resources/chat/chat.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chat: () => (/* binding */ Chat)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _completions_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./completions.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/chat/completions.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Chat extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor(){\n        super(...arguments);\n        this.completions = new _completions_mjs__WEBPACK_IMPORTED_MODULE_1__.Completions(this._client);\n    }\n}\n(function(Chat) {\n    Chat.Completions = _completions_mjs__WEBPACK_IMPORTED_MODULE_1__.Completions;\n})(Chat || (Chat = {})); //# sourceMappingURL=chat.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NoYXQvY2hhdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsc0ZBQXNGO0FBQ3JDO0FBQ0c7QUFDN0MsTUFBTUUsYUFBYUYsc0RBQVdBO0lBQ2pDRyxhQUFjO1FBQ1YsS0FBSyxJQUFJQztRQUNULElBQUksQ0FBQ0MsV0FBVyxHQUFHLElBQUlKLHlEQUEwQixDQUFDLElBQUksQ0FBQ00sT0FBTztJQUNsRTtBQUNKO0FBQ0MsVUFBVUwsSUFBSTtJQUNYQSxLQUFLSSxXQUFXLEdBQUdMLHlEQUEwQjtBQUNqRCxHQUFHQyxRQUFTQSxDQUFBQSxPQUFPLENBQUMsS0FDcEIsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmFyYW4tcm9hc3QtYm90Ly4vbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9jaGF0L2NoYXQubWpzPzU3ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgQ29tcGxldGlvbnNBUEkgZnJvbSBcIi4vY29tcGxldGlvbnMubWpzXCI7XG5leHBvcnQgY2xhc3MgQ2hhdCBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5jb21wbGV0aW9ucyA9IG5ldyBDb21wbGV0aW9uc0FQSS5Db21wbGV0aW9ucyh0aGlzLl9jbGllbnQpO1xuICAgIH1cbn1cbihmdW5jdGlvbiAoQ2hhdCkge1xuICAgIENoYXQuQ29tcGxldGlvbnMgPSBDb21wbGV0aW9uc0FQSS5Db21wbGV0aW9ucztcbn0pKENoYXQgfHwgKENoYXQgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2hhdC5tanMubWFwIl0sIm5hbWVzIjpbIkFQSVJlc291cmNlIiwiQ29tcGxldGlvbnNBUEkiLCJDaGF0IiwiY29uc3RydWN0b3IiLCJhcmd1bWVudHMiLCJjb21wbGV0aW9ucyIsIkNvbXBsZXRpb25zIiwiX2NsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/chat/chat.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/chat/completions.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/chat/completions.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Completions: () => (/* binding */ Completions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Completions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    create(body, options) {\n        return this._client.post(\"/openai/v1/chat/completions\", {\n            body,\n            ...options,\n            stream: body.stream ?? false\n        });\n    }\n}\n(function(Completions) {})(Completions || (Completions = {})); //# sourceMappingURL=completions.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NoYXQvY29tcGxldGlvbnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsc0ZBQXNGO0FBQ3JDO0FBQzFDLE1BQU1DLG9CQUFvQkQsc0RBQVdBO0lBQ3hDRSxPQUFPQyxJQUFJLEVBQUVDLE9BQU8sRUFBRTtRQUNsQixPQUFPLElBQUksQ0FBQ0MsT0FBTyxDQUFDQyxJQUFJLENBQUMsK0JBQStCO1lBQ3BESDtZQUNBLEdBQUdDLE9BQU87WUFDVkcsUUFBUUosS0FBS0ksTUFBTSxJQUFJO1FBQzNCO0lBQ0o7QUFDSjtBQUNDLFVBQVVOLFdBQVcsR0FDdEIsR0FBR0EsZUFBZ0JBLENBQUFBLGNBQWMsQ0FBQyxLQUNsQyx3Q0FBd0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYXJhbi1yb2FzdC1ib3QvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NoYXQvY29tcGxldGlvbnMubWpzPzdjYWYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIENvbXBsZXRpb25zIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9jaGF0L2NvbXBsZXRpb25zJywge1xuICAgICAgICAgICAgYm9keSxcbiAgICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgICAgICBzdHJlYW06IGJvZHkuc3RyZWFtID8/IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICB9XG59XG4oZnVuY3Rpb24gKENvbXBsZXRpb25zKSB7XG59KShDb21wbGV0aW9ucyB8fCAoQ29tcGxldGlvbnMgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tcGxldGlvbnMubWpzLm1hcCJdLCJuYW1lcyI6WyJBUElSZXNvdXJjZSIsIkNvbXBsZXRpb25zIiwiY3JlYXRlIiwiYm9keSIsIm9wdGlvbnMiLCJfY2xpZW50IiwicG9zdCIsInN0cmVhbSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/chat/completions.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/completions.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/completions.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Completions: () => (/* binding */ Completions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Completions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n}\n(function(Completions) {})(Completions || (Completions = {})); //# sourceMappingURL=completions.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NvbXBsZXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLHNGQUFzRjtBQUN4QztBQUN2QyxNQUFNQyxvQkFBb0JELHNEQUFXQTtBQUM1QztBQUNDLFVBQVVDLFdBQVcsR0FDdEIsR0FBR0EsZUFBZ0JBLENBQUFBLGNBQWMsQ0FBQyxLQUNsQyx3Q0FBd0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYXJhbi1yb2FzdC1ib3QvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NvbXBsZXRpb25zLm1qcz9jZjE3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi9yZXNvdXJjZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBDb21wbGV0aW9ucyBleHRlbmRzIEFQSVJlc291cmNlIHtcbn1cbihmdW5jdGlvbiAoQ29tcGxldGlvbnMpIHtcbn0pKENvbXBsZXRpb25zIHx8IChDb21wbGV0aW9ucyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb21wbGV0aW9ucy5tanMubWFwIl0sIm5hbWVzIjpbIkFQSVJlc291cmNlIiwiQ29tcGxldGlvbnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/completions.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/embeddings.mjs":
/*!********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/embeddings.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Embeddings: () => (/* binding */ Embeddings)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Embeddings extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Creates an embedding vector representing the input text.\n     */ create(body, options) {\n        return this._client.post(\"/openai/v1/embeddings\", {\n            body,\n            ...options\n        });\n    }\n}\n(function(Embeddings) {})(Embeddings || (Embeddings = {})); //# sourceMappingURL=embeddings.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2VtYmVkZGluZ3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsc0ZBQXNGO0FBQ3hDO0FBQ3ZDLE1BQU1DLG1CQUFtQkQsc0RBQVdBO0lBQ3ZDOztLQUVDLEdBQ0RFLE9BQU9DLElBQUksRUFBRUMsT0FBTyxFQUFFO1FBQ2xCLE9BQU8sSUFBSSxDQUFDQyxPQUFPLENBQUNDLElBQUksQ0FBQyx5QkFBeUI7WUFBRUg7WUFBTSxHQUFHQyxPQUFPO1FBQUM7SUFDekU7QUFDSjtBQUNDLFVBQVVILFVBQVUsR0FDckIsR0FBR0EsY0FBZUEsQ0FBQUEsYUFBYSxDQUFDLEtBQ2hDLHVDQUF1QyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhcmFuLXJvYXN0LWJvdC8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvZW1iZWRkaW5ncy5tanM/NzAwNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuaW1wb3J0IHsgQVBJUmVzb3VyY2UgfSBmcm9tIFwiLi4vcmVzb3VyY2UubWpzXCI7XG5leHBvcnQgY2xhc3MgRW1iZWRkaW5ncyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIGFuIGVtYmVkZGluZyB2ZWN0b3IgcmVwcmVzZW50aW5nIHRoZSBpbnB1dCB0ZXh0LlxuICAgICAqL1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9lbWJlZGRpbmdzJywgeyBib2R5LCAuLi5vcHRpb25zIH0pO1xuICAgIH1cbn1cbihmdW5jdGlvbiAoRW1iZWRkaW5ncykge1xufSkoRW1iZWRkaW5ncyB8fCAoRW1iZWRkaW5ncyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbWJlZGRpbmdzLm1qcy5tYXAiXSwibmFtZXMiOlsiQVBJUmVzb3VyY2UiLCJFbWJlZGRpbmdzIiwiY3JlYXRlIiwiYm9keSIsIm9wdGlvbnMiLCJfY2xpZW50IiwicG9zdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/embeddings.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/models.mjs":
/*!****************************************************!*\
  !*** ./node_modules/groq-sdk/resources/models.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Models: () => (/* binding */ Models)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Models extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Get a specific model\n     */ retrieve(model, options) {\n        return this._client.get(`/openai/v1/models/${model}`, options);\n    }\n    /**\n     * get all available models\n     */ list(options) {\n        return this._client.get(\"/openai/v1/models\", options);\n    }\n    /**\n     * Delete a model\n     */ delete(model, options) {\n        return this._client.delete(`/openai/v1/models/${model}`, options);\n    }\n}\n(function(Models) {})(Models || (Models = {})); //# sourceMappingURL=models.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL21vZGVscy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxzRkFBc0Y7QUFDeEM7QUFDdkMsTUFBTUMsZUFBZUQsc0RBQVdBO0lBQ25DOztLQUVDLEdBQ0RFLFNBQVNDLEtBQUssRUFBRUMsT0FBTyxFQUFFO1FBQ3JCLE9BQU8sSUFBSSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxDQUFDLGtCQUFrQixFQUFFSCxNQUFNLENBQUMsRUFBRUM7SUFDMUQ7SUFDQTs7S0FFQyxHQUNERyxLQUFLSCxPQUFPLEVBQUU7UUFDVixPQUFPLElBQUksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQUMscUJBQXFCRjtJQUNqRDtJQUNBOztLQUVDLEdBQ0RJLE9BQU9MLEtBQUssRUFBRUMsT0FBTyxFQUFFO1FBQ25CLE9BQU8sSUFBSSxDQUFDQyxPQUFPLENBQUNHLE1BQU0sQ0FBQyxDQUFDLGtCQUFrQixFQUFFTCxNQUFNLENBQUMsRUFBRUM7SUFDN0Q7QUFDSjtBQUNDLFVBQVVILE1BQU0sR0FDakIsR0FBR0EsVUFBV0EsQ0FBQUEsU0FBUyxDQUFDLEtBQ3hCLG1DQUFtQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhcmFuLXJvYXN0LWJvdC8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvbW9kZWxzLm1qcz9iNTk2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi9yZXNvdXJjZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBNb2RlbHMgZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogR2V0IGEgc3BlY2lmaWMgbW9kZWxcbiAgICAgKi9cbiAgICByZXRyaWV2ZShtb2RlbCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldChgL29wZW5haS92MS9tb2RlbHMvJHttb2RlbH1gLCBvcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogZ2V0IGFsbCBhdmFpbGFibGUgbW9kZWxzXG4gICAgICovXG4gICAgbGlzdChvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQuZ2V0KCcvb3BlbmFpL3YxL21vZGVscycsIG9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYSBtb2RlbFxuICAgICAqL1xuICAgIGRlbGV0ZShtb2RlbCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmRlbGV0ZShgL29wZW5haS92MS9tb2RlbHMvJHttb2RlbH1gLCBvcHRpb25zKTtcbiAgICB9XG59XG4oZnVuY3Rpb24gKE1vZGVscykge1xufSkoTW9kZWxzIHx8IChNb2RlbHMgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bW9kZWxzLm1qcy5tYXAiXSwibmFtZXMiOlsiQVBJUmVzb3VyY2UiLCJNb2RlbHMiLCJyZXRyaWV2ZSIsIm1vZGVsIiwib3B0aW9ucyIsIl9jbGllbnQiLCJnZXQiLCJsaXN0IiwiZGVsZXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/models.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/uploads.mjs":
/*!*******************************************!*\
  !*** ./node_modules/groq-sdk/uploads.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createForm: () => (/* binding */ createForm),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   isBlobLike: () => (/* binding */ isBlobLike),\n/* harmony export */   isFileLike: () => (/* binding */ isFileLike),\n/* harmony export */   isMultipartBody: () => (/* binding */ isMultipartBody),\n/* harmony export */   isResponseLike: () => (/* binding */ isResponseLike),\n/* harmony export */   isUploadable: () => (/* binding */ isUploadable),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* binding */ maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* binding */ multipartFormRequestOptions),\n/* harmony export */   toFile: () => (/* binding */ toFile)\n/* harmony export */ });\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n\n\nconst isResponseLike = (value)=>value != null && typeof value === \"object\" && typeof value.url === \"string\" && typeof value.blob === \"function\";\nconst isFileLike = (value)=>value != null && typeof value === \"object\" && typeof value.name === \"string\" && typeof value.lastModified === \"number\" && isBlobLike(value);\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */ const isBlobLike = (value)=>value != null && typeof value === \"object\" && typeof value.size === \"number\" && typeof value.type === \"string\" && typeof value.text === \"function\" && typeof value.slice === \"function\" && typeof value.arrayBuffer === \"function\";\nconst isUploadable = (value)=>{\n    return isFileLike(value) || isResponseLike(value) || (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream)(value);\n};\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */ async function toFile(value, name, options) {\n    // If it's a promise, resolve it.\n    value = await value;\n    // Use the file's options if there isn't one provided\n    options ?? (options = isFileLike(value) ? {\n        lastModified: value.lastModified,\n        type: value.type\n    } : {});\n    if (isResponseLike(value)) {\n        const blob = await value.blob();\n        name || (name = new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? \"unknown_file\");\n        return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File([\n            blob\n        ], name, options);\n    }\n    const bits = await getBytes(value);\n    name || (name = getName(value) ?? \"unknown_file\");\n    if (!options.type) {\n        const type = bits[0]?.type;\n        if (typeof type === \"string\") {\n            options = {\n                ...options,\n                type\n            };\n        }\n    }\n    return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File(bits, name, options);\n}\nasync function getBytes(value) {\n    let parts = [];\n    if (typeof value === \"string\" || ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n    value instanceof ArrayBuffer) {\n        parts.push(value);\n    } else if (isBlobLike(value)) {\n        parts.push(await value.arrayBuffer());\n    } else if (isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n    ) {\n        for await (const chunk of value){\n            parts.push(chunk); // TODO, consider validating?\n        }\n    } else {\n        throw new Error(`Unexpected data type: ${typeof value}; constructor: ${value?.constructor?.name}; props: ${propsForError(value)}`);\n    }\n    return parts;\n}\nfunction propsForError(value) {\n    const props = Object.getOwnPropertyNames(value);\n    return `[${props.map((p)=>`\"${p}\"`).join(\", \")}]`;\n}\nfunction getName(value) {\n    return getStringFromMaybeBuffer(value.name) || getStringFromMaybeBuffer(value.filename) || // For fs.ReadStream\n    getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop();\n}\nconst getStringFromMaybeBuffer = (x)=>{\n    if (typeof x === \"string\") return x;\n    if (typeof Buffer !== \"undefined\" && x instanceof Buffer) return String(x);\n    return undefined;\n};\nconst isAsyncIterableIterator = (value)=>value != null && typeof value === \"object\" && typeof value[Symbol.asyncIterator] === \"function\";\nconst isMultipartBody = (body)=>body && typeof body === \"object\" && body.body && body[Symbol.toStringTag] === \"MultipartBody\";\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */ const maybeMultipartFormRequestOptions = async (opts)=>{\n    if (!hasUploadableValue(opts.body)) return opts;\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst multipartFormRequestOptions = async (opts)=>{\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst createForm = async (body)=>{\n    const form = new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData();\n    await Promise.all(Object.entries(body || {}).map(([key, value])=>addFormValue(form, key, value)));\n    return form;\n};\nconst hasUploadableValue = (value)=>{\n    if (isUploadable(value)) return true;\n    if (Array.isArray(value)) return value.some(hasUploadableValue);\n    if (value && typeof value === \"object\") {\n        for(const k in value){\n            if (hasUploadableValue(value[k])) return true;\n        }\n    }\n    return false;\n};\nconst addFormValue = async (form, key, value)=>{\n    if (value === undefined) return;\n    if (value == null) {\n        throw new TypeError(`Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`);\n    }\n    // TODO: make nested formats configurable\n    if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n        form.append(key, String(value));\n    } else if (isUploadable(value)) {\n        const file = await toFile(value);\n        form.append(key, file);\n    } else if (Array.isArray(value)) {\n        await Promise.all(value.map((entry)=>addFormValue(form, key + \"[]\", entry)));\n    } else if (typeof value === \"object\") {\n        await Promise.all(Object.entries(value).map(([name, prop])=>addFormValue(form, `${key}[${name}]`, prop)));\n    } else {\n        throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);\n    }\n}; //# sourceMappingURL=uploads.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/uploads.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/version.mjs":
/*!*******************************************!*\
  !*** ./node_modules/groq-sdk/version.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = \"0.7.0\"; // x-release-please-version\n //# sourceMappingURL=version.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvdmVyc2lvbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLFVBQVUsUUFBUSxDQUFDLDJCQUEyQjtDQUMzRCxvQ0FBb0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYXJhbi1yb2FzdC1ib3QvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvdmVyc2lvbi5tanM/OTVmYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVkVSU0lPTiA9ICcwLjcuMCc7IC8vIHgtcmVsZWFzZS1wbGVhc2UtdmVyc2lvblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5tanMubWFwIl0sIm5hbWVzIjpbIlZFUlNJT04iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/version.mjs\n");

/***/ })

};
;