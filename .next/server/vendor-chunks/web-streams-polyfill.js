"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-streams-polyfill";
exports.ids = ["vendor-chunks/web-streams-polyfill"];
exports.modules = {

/***/ "(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/web-streams-polyfill/dist/ponyfill.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ByteLengthQueuingStrategy: () => (/* binding */ ByteLengthQueuingStrategy),\n/* harmony export */   CountQueuingStrategy: () => (/* binding */ CountQueuingStrategy),\n/* harmony export */   ReadableByteStreamController: () => (/* binding */ ReadableByteStreamController),\n/* harmony export */   ReadableStream: () => (/* binding */ ReadableStream),\n/* harmony export */   ReadableStreamBYOBReader: () => (/* binding */ ReadableStreamBYOBReader),\n/* harmony export */   ReadableStreamBYOBRequest: () => (/* binding */ ReadableStreamBYOBRequest),\n/* harmony export */   ReadableStreamDefaultController: () => (/* binding */ ReadableStreamDefaultController),\n/* harmony export */   ReadableStreamDefaultReader: () => (/* binding */ ReadableStreamDefaultReader),\n/* harmony export */   TransformStream: () => (/* binding */ TransformStream),\n/* harmony export */   TransformStreamDefaultController: () => (/* binding */ TransformStreamDefaultController),\n/* harmony export */   WritableStream: () => (/* binding */ WritableStream),\n/* harmony export */   WritableStreamDefaultController: () => (/* binding */ WritableStreamDefaultController),\n/* harmony export */   WritableStreamDefaultWriter: () => (/* binding */ WritableStreamDefaultWriter)\n/* harmony export */ });\n/**\n * @license\n * web-streams-polyfill v4.0.0-beta.3\n * Copyright 2021 Mattias Buelens, Diwank Singh Tomer and other contributors.\n * This code is released under the MIT license.\n * SPDX-License-Identifier: MIT\n */ const e = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? Symbol : (e)=>`Symbol(${e})`;\nfunction t() {}\nfunction r(e) {\n    return \"object\" == typeof e && null !== e || \"function\" == typeof e;\n}\nconst o = t;\nfunction n(e, t) {\n    try {\n        Object.defineProperty(e, \"name\", {\n            value: t,\n            configurable: !0\n        });\n    } catch (e) {}\n}\nconst a = Promise, i = Promise.prototype.then, l = Promise.resolve.bind(a), s = Promise.reject.bind(a);\nfunction u(e) {\n    return new a(e);\n}\nfunction c(e) {\n    return l(e);\n}\nfunction d(e) {\n    return s(e);\n}\nfunction f(e, t, r) {\n    return i.call(e, t, r);\n}\nfunction b(e, t, r) {\n    f(f(e, t, r), void 0, o);\n}\nfunction h(e, t) {\n    b(e, t);\n}\nfunction _(e, t) {\n    b(e, void 0, t);\n}\nfunction p(e, t, r) {\n    return f(e, t, r);\n}\nfunction m(e) {\n    f(e, void 0, o);\n}\nlet y = (e)=>{\n    if (\"function\" == typeof queueMicrotask) y = queueMicrotask;\n    else {\n        const e = c(void 0);\n        y = (t)=>f(e, t);\n    }\n    return y(e);\n};\nfunction g(e, t, r) {\n    if (\"function\" != typeof e) throw new TypeError(\"Argument is not a function\");\n    return Function.prototype.apply.call(e, t, r);\n}\nfunction w(e, t, r) {\n    try {\n        return c(g(e, t, r));\n    } catch (e) {\n        return d(e);\n    }\n}\nclass S {\n    constructor(){\n        this._cursor = 0, this._size = 0, this._front = {\n            _elements: [],\n            _next: void 0\n        }, this._back = this._front, this._cursor = 0, this._size = 0;\n    }\n    get length() {\n        return this._size;\n    }\n    push(e) {\n        const t = this._back;\n        let r = t;\n        16383 === t._elements.length && (r = {\n            _elements: [],\n            _next: void 0\n        }), t._elements.push(e), r !== t && (this._back = r, t._next = r), ++this._size;\n    }\n    shift() {\n        const e = this._front;\n        let t = e;\n        const r = this._cursor;\n        let o = r + 1;\n        const n = e._elements, a = n[r];\n        return 16384 === o && (t = e._next, o = 0), --this._size, this._cursor = o, e !== t && (this._front = t), n[r] = void 0, a;\n    }\n    forEach(e) {\n        let t = this._cursor, r = this._front, o = r._elements;\n        for(; !(t === o.length && void 0 === r._next || t === o.length && (r = r._next, o = r._elements, t = 0, 0 === o.length));)e(o[t]), ++t;\n    }\n    peek() {\n        const e = this._front, t = this._cursor;\n        return e._elements[t];\n    }\n}\nconst v = e(\"[[AbortSteps]]\"), R = e(\"[[ErrorSteps]]\"), T = e(\"[[CancelSteps]]\"), q = e(\"[[PullSteps]]\"), C = e(\"[[ReleaseSteps]]\");\nfunction E(e, t) {\n    e._ownerReadableStream = t, t._reader = e, \"readable\" === t._state ? O(e) : \"closed\" === t._state ? function(e) {\n        O(e), j(e);\n    }(e) : B(e, t._storedError);\n}\nfunction P(e, t) {\n    return Gt(e._ownerReadableStream, t);\n}\nfunction W(e) {\n    const t = e._ownerReadableStream;\n    \"readable\" === t._state ? A(e, new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")) : function(e, t) {\n        B(e, t);\n    }(e, new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")), t._readableStreamController[C](), t._reader = void 0, e._ownerReadableStream = void 0;\n}\nfunction k(e) {\n    return new TypeError(\"Cannot \" + e + \" a stream using a released reader\");\n}\nfunction O(e) {\n    e._closedPromise = u((t, r)=>{\n        e._closedPromise_resolve = t, e._closedPromise_reject = r;\n    });\n}\nfunction B(e, t) {\n    O(e), A(e, t);\n}\nfunction A(e, t) {\n    void 0 !== e._closedPromise_reject && (m(e._closedPromise), e._closedPromise_reject(t), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0);\n}\nfunction j(e) {\n    void 0 !== e._closedPromise_resolve && (e._closedPromise_resolve(void 0), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0);\n}\nconst z = Number.isFinite || function(e) {\n    return \"number\" == typeof e && isFinite(e);\n}, L = Math.trunc || function(e) {\n    return e < 0 ? Math.ceil(e) : Math.floor(e);\n};\nfunction F(e, t) {\n    if (void 0 !== e && \"object\" != typeof (r = e) && \"function\" != typeof r) throw new TypeError(`${t} is not an object.`);\n    var r;\n}\nfunction I(e, t) {\n    if (\"function\" != typeof e) throw new TypeError(`${t} is not a function.`);\n}\nfunction D(e, t) {\n    if (!function(e) {\n        return \"object\" == typeof e && null !== e || \"function\" == typeof e;\n    }(e)) throw new TypeError(`${t} is not an object.`);\n}\nfunction $(e, t, r) {\n    if (void 0 === e) throw new TypeError(`Parameter ${t} is required in '${r}'.`);\n}\nfunction M(e, t, r) {\n    if (void 0 === e) throw new TypeError(`${t} is required in '${r}'.`);\n}\nfunction Y(e) {\n    return Number(e);\n}\nfunction Q(e) {\n    return 0 === e ? 0 : e;\n}\nfunction N(e, t) {\n    const r = Number.MAX_SAFE_INTEGER;\n    let o = Number(e);\n    if (o = Q(o), !z(o)) throw new TypeError(`${t} is not a finite number`);\n    if (o = function(e) {\n        return Q(L(e));\n    }(o), o < 0 || o > r) throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);\n    return z(o) && 0 !== o ? o : 0;\n}\nfunction H(e) {\n    if (!r(e)) return !1;\n    if (\"function\" != typeof e.getReader) return !1;\n    try {\n        return \"boolean\" == typeof e.locked;\n    } catch (e) {\n        return !1;\n    }\n}\nfunction x(e) {\n    if (!r(e)) return !1;\n    if (\"function\" != typeof e.getWriter) return !1;\n    try {\n        return \"boolean\" == typeof e.locked;\n    } catch (e) {\n        return !1;\n    }\n}\nfunction V(e, t) {\n    if (!Vt(e)) throw new TypeError(`${t} is not a ReadableStream.`);\n}\nfunction U(e, t) {\n    e._reader._readRequests.push(t);\n}\nfunction G(e, t, r) {\n    const o = e._reader._readRequests.shift();\n    r ? o._closeSteps() : o._chunkSteps(t);\n}\nfunction X(e) {\n    return e._reader._readRequests.length;\n}\nfunction J(e) {\n    const t = e._reader;\n    return void 0 !== t && !!K(t);\n}\nclass ReadableStreamDefaultReader {\n    constructor(e){\n        if ($(e, 1, \"ReadableStreamDefaultReader\"), V(e, \"First parameter\"), Ut(e)) throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");\n        E(this, e), this._readRequests = new S;\n    }\n    get closed() {\n        return K(this) ? this._closedPromise : d(ee(\"closed\"));\n    }\n    cancel(e) {\n        return K(this) ? void 0 === this._ownerReadableStream ? d(k(\"cancel\")) : P(this, e) : d(ee(\"cancel\"));\n    }\n    read() {\n        if (!K(this)) return d(ee(\"read\"));\n        if (void 0 === this._ownerReadableStream) return d(k(\"read from\"));\n        let e, t;\n        const r = u((r, o)=>{\n            e = r, t = o;\n        });\n        return function(e, t) {\n            const r = e._ownerReadableStream;\n            r._disturbed = !0, \"closed\" === r._state ? t._closeSteps() : \"errored\" === r._state ? t._errorSteps(r._storedError) : r._readableStreamController[q](t);\n        }(this, {\n            _chunkSteps: (t)=>e({\n                    value: t,\n                    done: !1\n                }),\n            _closeSteps: ()=>e({\n                    value: void 0,\n                    done: !0\n                }),\n            _errorSteps: (e)=>t(e)\n        }), r;\n    }\n    releaseLock() {\n        if (!K(this)) throw ee(\"releaseLock\");\n        void 0 !== this._ownerReadableStream && function(e) {\n            W(e);\n            const t = new TypeError(\"Reader was released\");\n            Z(e, t);\n        }(this);\n    }\n}\nfunction K(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_readRequests\") && e instanceof ReadableStreamDefaultReader;\n}\nfunction Z(e, t) {\n    const r = e._readRequests;\n    e._readRequests = new S, r.forEach((e)=>{\n        e._errorSteps(t);\n    });\n}\nfunction ee(e) {\n    return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`);\n}\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n    cancel: {\n        enumerable: !0\n    },\n    read: {\n        enumerable: !0\n    },\n    releaseLock: {\n        enumerable: !0\n    },\n    closed: {\n        enumerable: !0\n    }\n}), n(ReadableStreamDefaultReader.prototype.cancel, \"cancel\"), n(ReadableStreamDefaultReader.prototype.read, \"read\"), n(ReadableStreamDefaultReader.prototype.releaseLock, \"releaseLock\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(ReadableStreamDefaultReader.prototype, e.toStringTag, {\n    value: \"ReadableStreamDefaultReader\",\n    configurable: !0\n});\nclass te {\n    constructor(e, t){\n        this._ongoingPromise = void 0, this._isFinished = !1, this._reader = e, this._preventCancel = t;\n    }\n    next() {\n        const e = ()=>this._nextSteps();\n        return this._ongoingPromise = this._ongoingPromise ? p(this._ongoingPromise, e, e) : e(), this._ongoingPromise;\n    }\n    return(e) {\n        const t = ()=>this._returnSteps(e);\n        return this._ongoingPromise ? p(this._ongoingPromise, t, t) : t();\n    }\n    _nextSteps() {\n        if (this._isFinished) return Promise.resolve({\n            value: void 0,\n            done: !0\n        });\n        const e = this._reader;\n        return void 0 === e ? d(k(\"iterate\")) : f(e.read(), (e)=>{\n            var t;\n            return this._ongoingPromise = void 0, e.done && (this._isFinished = !0, null === (t = this._reader) || void 0 === t || t.releaseLock(), this._reader = void 0), e;\n        }, (e)=>{\n            var t;\n            throw this._ongoingPromise = void 0, this._isFinished = !0, null === (t = this._reader) || void 0 === t || t.releaseLock(), this._reader = void 0, e;\n        });\n    }\n    _returnSteps(e) {\n        if (this._isFinished) return Promise.resolve({\n            value: e,\n            done: !0\n        });\n        this._isFinished = !0;\n        const t = this._reader;\n        if (void 0 === t) return d(k(\"finish iterating\"));\n        if (this._reader = void 0, !this._preventCancel) {\n            const r = t.cancel(e);\n            return t.releaseLock(), p(r, ()=>({\n                    value: e,\n                    done: !0\n                }));\n        }\n        return t.releaseLock(), c({\n            value: e,\n            done: !0\n        });\n    }\n}\nconst re = {\n    next () {\n        return oe(this) ? this._asyncIteratorImpl.next() : d(ne(\"next\"));\n    },\n    return (e) {\n        return oe(this) ? this._asyncIteratorImpl.return(e) : d(ne(\"return\"));\n    }\n};\nfunction oe(e) {\n    if (!r(e)) return !1;\n    if (!Object.prototype.hasOwnProperty.call(e, \"_asyncIteratorImpl\")) return !1;\n    try {\n        return e._asyncIteratorImpl instanceof te;\n    } catch (e) {\n        return !1;\n    }\n}\nfunction ne(e) {\n    return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`);\n}\n\"symbol\" == typeof e.asyncIterator && Object.defineProperty(re, e.asyncIterator, {\n    value () {\n        return this;\n    },\n    writable: !0,\n    configurable: !0\n});\nconst ae = Number.isNaN || function(e) {\n    return e != e;\n};\nfunction ie(e, t, r, o, n) {\n    new Uint8Array(e).set(new Uint8Array(r, o, n), t);\n}\nfunction le(e) {\n    const t = function(e, t, r) {\n        if (e.slice) return e.slice(t, r);\n        const o = r - t, n = new ArrayBuffer(o);\n        return ie(n, 0, e, t, o), n;\n    }(e.buffer, e.byteOffset, e.byteOffset + e.byteLength);\n    return new Uint8Array(t);\n}\nfunction se(e) {\n    const t = e._queue.shift();\n    return e._queueTotalSize -= t.size, e._queueTotalSize < 0 && (e._queueTotalSize = 0), t.value;\n}\nfunction ue(e, t, r) {\n    if (\"number\" != typeof (o = r) || ae(o) || o < 0 || r === 1 / 0) throw new RangeError(\"Size must be a finite, non-NaN, non-negative number.\");\n    var o;\n    e._queue.push({\n        value: t,\n        size: r\n    }), e._queueTotalSize += r;\n}\nfunction ce(e) {\n    e._queue = new S, e._queueTotalSize = 0;\n}\nclass ReadableStreamBYOBRequest {\n    constructor(){\n        throw new TypeError(\"Illegal constructor\");\n    }\n    get view() {\n        if (!fe(this)) throw Be(\"view\");\n        return this._view;\n    }\n    respond(e) {\n        if (!fe(this)) throw Be(\"respond\");\n        if ($(e, 1, \"respond\"), e = N(e, \"First parameter\"), void 0 === this._associatedReadableByteStreamController) throw new TypeError(\"This BYOB request has been invalidated\");\n        this._view.buffer, function(e, t) {\n            const r = e._pendingPullIntos.peek();\n            if (\"closed\" === e._controlledReadableByteStream._state) {\n                if (0 !== t) throw new TypeError(\"bytesWritten must be 0 when calling respond() on a closed stream\");\n            } else {\n                if (0 === t) throw new TypeError(\"bytesWritten must be greater than 0 when calling respond() on a readable stream\");\n                if (r.bytesFilled + t > r.byteLength) throw new RangeError(\"bytesWritten out of range\");\n            }\n            r.buffer = r.buffer, qe(e, t);\n        }(this._associatedReadableByteStreamController, e);\n    }\n    respondWithNewView(e) {\n        if (!fe(this)) throw Be(\"respondWithNewView\");\n        if ($(e, 1, \"respondWithNewView\"), !ArrayBuffer.isView(e)) throw new TypeError(\"You can only respond with array buffer views\");\n        if (void 0 === this._associatedReadableByteStreamController) throw new TypeError(\"This BYOB request has been invalidated\");\n        e.buffer, function(e, t) {\n            const r = e._pendingPullIntos.peek();\n            if (\"closed\" === e._controlledReadableByteStream._state) {\n                if (0 !== t.byteLength) throw new TypeError(\"The view's length must be 0 when calling respondWithNewView() on a closed stream\");\n            } else if (0 === t.byteLength) throw new TypeError(\"The view's length must be greater than 0 when calling respondWithNewView() on a readable stream\");\n            if (r.byteOffset + r.bytesFilled !== t.byteOffset) throw new RangeError(\"The region specified by view does not match byobRequest\");\n            if (r.bufferByteLength !== t.buffer.byteLength) throw new RangeError(\"The buffer of view has different capacity than byobRequest\");\n            if (r.bytesFilled + t.byteLength > r.byteLength) throw new RangeError(\"The region specified by view is larger than byobRequest\");\n            const o = t.byteLength;\n            r.buffer = t.buffer, qe(e, o);\n        }(this._associatedReadableByteStreamController, e);\n    }\n}\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n    respond: {\n        enumerable: !0\n    },\n    respondWithNewView: {\n        enumerable: !0\n    },\n    view: {\n        enumerable: !0\n    }\n}), n(ReadableStreamBYOBRequest.prototype.respond, \"respond\"), n(ReadableStreamBYOBRequest.prototype.respondWithNewView, \"respondWithNewView\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(ReadableStreamBYOBRequest.prototype, e.toStringTag, {\n    value: \"ReadableStreamBYOBRequest\",\n    configurable: !0\n});\nclass ReadableByteStreamController {\n    constructor(){\n        throw new TypeError(\"Illegal constructor\");\n    }\n    get byobRequest() {\n        if (!de(this)) throw Ae(\"byobRequest\");\n        return function(e) {\n            if (null === e._byobRequest && e._pendingPullIntos.length > 0) {\n                const t = e._pendingPullIntos.peek(), r = new Uint8Array(t.buffer, t.byteOffset + t.bytesFilled, t.byteLength - t.bytesFilled), o = Object.create(ReadableStreamBYOBRequest.prototype);\n                !function(e, t, r) {\n                    e._associatedReadableByteStreamController = t, e._view = r;\n                }(o, e, r), e._byobRequest = o;\n            }\n            return e._byobRequest;\n        }(this);\n    }\n    get desiredSize() {\n        if (!de(this)) throw Ae(\"desiredSize\");\n        return ke(this);\n    }\n    close() {\n        if (!de(this)) throw Ae(\"close\");\n        if (this._closeRequested) throw new TypeError(\"The stream has already been closed; do not close it again!\");\n        const e = this._controlledReadableByteStream._state;\n        if (\"readable\" !== e) throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);\n        !function(e) {\n            const t = e._controlledReadableByteStream;\n            if (e._closeRequested || \"readable\" !== t._state) return;\n            if (e._queueTotalSize > 0) return void (e._closeRequested = !0);\n            if (e._pendingPullIntos.length > 0) {\n                if (e._pendingPullIntos.peek().bytesFilled > 0) {\n                    const t = new TypeError(\"Insufficient bytes to fill elements in the given buffer\");\n                    throw Pe(e, t), t;\n                }\n            }\n            Ee(e), Xt(t);\n        }(this);\n    }\n    enqueue(e) {\n        if (!de(this)) throw Ae(\"enqueue\");\n        if ($(e, 1, \"enqueue\"), !ArrayBuffer.isView(e)) throw new TypeError(\"chunk must be an array buffer view\");\n        if (0 === e.byteLength) throw new TypeError(\"chunk must have non-zero byteLength\");\n        if (0 === e.buffer.byteLength) throw new TypeError(\"chunk's buffer must have non-zero byteLength\");\n        if (this._closeRequested) throw new TypeError(\"stream is closed or draining\");\n        const t = this._controlledReadableByteStream._state;\n        if (\"readable\" !== t) throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);\n        !function(e, t) {\n            const r = e._controlledReadableByteStream;\n            if (e._closeRequested || \"readable\" !== r._state) return;\n            const o = t.buffer, n = t.byteOffset, a = t.byteLength, i = o;\n            if (e._pendingPullIntos.length > 0) {\n                const t = e._pendingPullIntos.peek();\n                t.buffer, Re(e), t.buffer = t.buffer, \"none\" === t.readerType && ge(e, t);\n            }\n            if (J(r)) if (function(e) {\n                const t = e._controlledReadableByteStream._reader;\n                for(; t._readRequests.length > 0;){\n                    if (0 === e._queueTotalSize) return;\n                    We(e, t._readRequests.shift());\n                }\n            }(e), 0 === X(r)) me(e, i, n, a);\n            else {\n                e._pendingPullIntos.length > 0 && Ce(e);\n                G(r, new Uint8Array(i, n, a), !1);\n            }\n            else Le(r) ? (me(e, i, n, a), Te(e)) : me(e, i, n, a);\n            be(e);\n        }(this, e);\n    }\n    error(e) {\n        if (!de(this)) throw Ae(\"error\");\n        Pe(this, e);\n    }\n    [T](e) {\n        he(this), ce(this);\n        const t = this._cancelAlgorithm(e);\n        return Ee(this), t;\n    }\n    [q](e) {\n        const t = this._controlledReadableByteStream;\n        if (this._queueTotalSize > 0) return void We(this, e);\n        const r = this._autoAllocateChunkSize;\n        if (void 0 !== r) {\n            let t;\n            try {\n                t = new ArrayBuffer(r);\n            } catch (t) {\n                return void e._errorSteps(t);\n            }\n            const o = {\n                buffer: t,\n                bufferByteLength: r,\n                byteOffset: 0,\n                byteLength: r,\n                bytesFilled: 0,\n                elementSize: 1,\n                viewConstructor: Uint8Array,\n                readerType: \"default\"\n            };\n            this._pendingPullIntos.push(o);\n        }\n        U(t, e), be(this);\n    }\n    [C]() {\n        if (this._pendingPullIntos.length > 0) {\n            const e = this._pendingPullIntos.peek();\n            e.readerType = \"none\", this._pendingPullIntos = new S, this._pendingPullIntos.push(e);\n        }\n    }\n}\nfunction de(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_controlledReadableByteStream\") && e instanceof ReadableByteStreamController;\n}\nfunction fe(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_associatedReadableByteStreamController\") && e instanceof ReadableStreamBYOBRequest;\n}\nfunction be(e) {\n    const t = function(e) {\n        const t = e._controlledReadableByteStream;\n        if (\"readable\" !== t._state) return !1;\n        if (e._closeRequested) return !1;\n        if (!e._started) return !1;\n        if (J(t) && X(t) > 0) return !0;\n        if (Le(t) && ze(t) > 0) return !0;\n        if (ke(e) > 0) return !0;\n        return !1;\n    }(e);\n    if (!t) return;\n    if (e._pulling) return void (e._pullAgain = !0);\n    e._pulling = !0;\n    b(e._pullAlgorithm(), ()=>(e._pulling = !1, e._pullAgain && (e._pullAgain = !1, be(e)), null), (t)=>(Pe(e, t), null));\n}\nfunction he(e) {\n    Re(e), e._pendingPullIntos = new S;\n}\nfunction _e(e, t) {\n    let r = !1;\n    \"closed\" === e._state && (r = !0);\n    const o = pe(t);\n    \"default\" === t.readerType ? G(e, o, r) : function(e, t, r) {\n        const o = e._reader._readIntoRequests.shift();\n        r ? o._closeSteps(t) : o._chunkSteps(t);\n    }(e, o, r);\n}\nfunction pe(e) {\n    const t = e.bytesFilled, r = e.elementSize;\n    return new e.viewConstructor(e.buffer, e.byteOffset, t / r);\n}\nfunction me(e, t, r, o) {\n    e._queue.push({\n        buffer: t,\n        byteOffset: r,\n        byteLength: o\n    }), e._queueTotalSize += o;\n}\nfunction ye(e, t, r, o) {\n    let n;\n    try {\n        n = t.slice(r, r + o);\n    } catch (t) {\n        throw Pe(e, t), t;\n    }\n    me(e, n, 0, o);\n}\nfunction ge(e, t) {\n    t.bytesFilled > 0 && ye(e, t.buffer, t.byteOffset, t.bytesFilled), Ce(e);\n}\nfunction we(e, t) {\n    const r = t.elementSize, o = t.bytesFilled - t.bytesFilled % r, n = Math.min(e._queueTotalSize, t.byteLength - t.bytesFilled), a = t.bytesFilled + n, i = a - a % r;\n    let l = n, s = !1;\n    i > o && (l = i - t.bytesFilled, s = !0);\n    const u = e._queue;\n    for(; l > 0;){\n        const r = u.peek(), o = Math.min(l, r.byteLength), n = t.byteOffset + t.bytesFilled;\n        ie(t.buffer, n, r.buffer, r.byteOffset, o), r.byteLength === o ? u.shift() : (r.byteOffset += o, r.byteLength -= o), e._queueTotalSize -= o, Se(e, o, t), l -= o;\n    }\n    return s;\n}\nfunction Se(e, t, r) {\n    r.bytesFilled += t;\n}\nfunction ve(e) {\n    0 === e._queueTotalSize && e._closeRequested ? (Ee(e), Xt(e._controlledReadableByteStream)) : be(e);\n}\nfunction Re(e) {\n    null !== e._byobRequest && (e._byobRequest._associatedReadableByteStreamController = void 0, e._byobRequest._view = null, e._byobRequest = null);\n}\nfunction Te(e) {\n    for(; e._pendingPullIntos.length > 0;){\n        if (0 === e._queueTotalSize) return;\n        const t = e._pendingPullIntos.peek();\n        we(e, t) && (Ce(e), _e(e._controlledReadableByteStream, t));\n    }\n}\nfunction qe(e, t) {\n    const r = e._pendingPullIntos.peek();\n    Re(e);\n    \"closed\" === e._controlledReadableByteStream._state ? function(e, t) {\n        \"none\" === t.readerType && Ce(e);\n        const r = e._controlledReadableByteStream;\n        if (Le(r)) for(; ze(r) > 0;)_e(r, Ce(e));\n    }(e, r) : function(e, t, r) {\n        if (Se(0, t, r), \"none\" === r.readerType) return ge(e, r), void Te(e);\n        if (r.bytesFilled < r.elementSize) return;\n        Ce(e);\n        const o = r.bytesFilled % r.elementSize;\n        if (o > 0) {\n            const t = r.byteOffset + r.bytesFilled;\n            ye(e, r.buffer, t - o, o);\n        }\n        r.bytesFilled -= o, _e(e._controlledReadableByteStream, r), Te(e);\n    }(e, t, r), be(e);\n}\nfunction Ce(e) {\n    return e._pendingPullIntos.shift();\n}\nfunction Ee(e) {\n    e._pullAlgorithm = void 0, e._cancelAlgorithm = void 0;\n}\nfunction Pe(e, t) {\n    const r = e._controlledReadableByteStream;\n    \"readable\" === r._state && (he(e), ce(e), Ee(e), Jt(r, t));\n}\nfunction We(e, t) {\n    const r = e._queue.shift();\n    e._queueTotalSize -= r.byteLength, ve(e);\n    const o = new Uint8Array(r.buffer, r.byteOffset, r.byteLength);\n    t._chunkSteps(o);\n}\nfunction ke(e) {\n    const t = e._controlledReadableByteStream._state;\n    return \"errored\" === t ? null : \"closed\" === t ? 0 : e._strategyHWM - e._queueTotalSize;\n}\nfunction Oe(e, t, r) {\n    const o = Object.create(ReadableByteStreamController.prototype);\n    let n, a, i;\n    n = void 0 !== t.start ? ()=>t.start(o) : ()=>{}, a = void 0 !== t.pull ? ()=>t.pull(o) : ()=>c(void 0), i = void 0 !== t.cancel ? (e)=>t.cancel(e) : ()=>c(void 0);\n    const l = t.autoAllocateChunkSize;\n    if (0 === l) throw new TypeError(\"autoAllocateChunkSize must be greater than 0\");\n    !function(e, t, r, o, n, a, i) {\n        t._controlledReadableByteStream = e, t._pullAgain = !1, t._pulling = !1, t._byobRequest = null, t._queue = t._queueTotalSize = void 0, ce(t), t._closeRequested = !1, t._started = !1, t._strategyHWM = a, t._pullAlgorithm = o, t._cancelAlgorithm = n, t._autoAllocateChunkSize = i, t._pendingPullIntos = new S, e._readableStreamController = t, b(c(r()), ()=>(t._started = !0, be(t), null), (e)=>(Pe(t, e), null));\n    }(e, o, n, a, i, r, l);\n}\nfunction Be(e) {\n    return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`);\n}\nfunction Ae(e) {\n    return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`);\n}\nfunction je(e, t) {\n    e._reader._readIntoRequests.push(t);\n}\nfunction ze(e) {\n    return e._reader._readIntoRequests.length;\n}\nfunction Le(e) {\n    const t = e._reader;\n    return void 0 !== t && !!Fe(t);\n}\nObject.defineProperties(ReadableByteStreamController.prototype, {\n    close: {\n        enumerable: !0\n    },\n    enqueue: {\n        enumerable: !0\n    },\n    error: {\n        enumerable: !0\n    },\n    byobRequest: {\n        enumerable: !0\n    },\n    desiredSize: {\n        enumerable: !0\n    }\n}), n(ReadableByteStreamController.prototype.close, \"close\"), n(ReadableByteStreamController.prototype.enqueue, \"enqueue\"), n(ReadableByteStreamController.prototype.error, \"error\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(ReadableByteStreamController.prototype, e.toStringTag, {\n    value: \"ReadableByteStreamController\",\n    configurable: !0\n});\nclass ReadableStreamBYOBReader {\n    constructor(e){\n        if ($(e, 1, \"ReadableStreamBYOBReader\"), V(e, \"First parameter\"), Ut(e)) throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");\n        if (!de(e._readableStreamController)) throw new TypeError(\"Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source\");\n        E(this, e), this._readIntoRequests = new S;\n    }\n    get closed() {\n        return Fe(this) ? this._closedPromise : d(De(\"closed\"));\n    }\n    cancel(e) {\n        return Fe(this) ? void 0 === this._ownerReadableStream ? d(k(\"cancel\")) : P(this, e) : d(De(\"cancel\"));\n    }\n    read(e) {\n        if (!Fe(this)) return d(De(\"read\"));\n        if (!ArrayBuffer.isView(e)) return d(new TypeError(\"view must be an array buffer view\"));\n        if (0 === e.byteLength) return d(new TypeError(\"view must have non-zero byteLength\"));\n        if (0 === e.buffer.byteLength) return d(new TypeError(\"view's buffer must have non-zero byteLength\"));\n        if (e.buffer, void 0 === this._ownerReadableStream) return d(k(\"read from\"));\n        let t, r;\n        const o = u((e, o)=>{\n            t = e, r = o;\n        });\n        return function(e, t, r) {\n            const o = e._ownerReadableStream;\n            o._disturbed = !0, \"errored\" === o._state ? r._errorSteps(o._storedError) : function(e, t, r) {\n                const o = e._controlledReadableByteStream;\n                let n = 1;\n                t.constructor !== DataView && (n = t.constructor.BYTES_PER_ELEMENT);\n                const a = t.constructor, i = t.buffer, l = {\n                    buffer: i,\n                    bufferByteLength: i.byteLength,\n                    byteOffset: t.byteOffset,\n                    byteLength: t.byteLength,\n                    bytesFilled: 0,\n                    elementSize: n,\n                    viewConstructor: a,\n                    readerType: \"byob\"\n                };\n                if (e._pendingPullIntos.length > 0) return e._pendingPullIntos.push(l), void je(o, r);\n                if (\"closed\" !== o._state) {\n                    if (e._queueTotalSize > 0) {\n                        if (we(e, l)) {\n                            const t = pe(l);\n                            return ve(e), void r._chunkSteps(t);\n                        }\n                        if (e._closeRequested) {\n                            const t = new TypeError(\"Insufficient bytes to fill elements in the given buffer\");\n                            return Pe(e, t), void r._errorSteps(t);\n                        }\n                    }\n                    e._pendingPullIntos.push(l), je(o, r), be(e);\n                } else {\n                    const e = new a(l.buffer, l.byteOffset, 0);\n                    r._closeSteps(e);\n                }\n            }(o._readableStreamController, t, r);\n        }(this, e, {\n            _chunkSteps: (e)=>t({\n                    value: e,\n                    done: !1\n                }),\n            _closeSteps: (e)=>t({\n                    value: e,\n                    done: !0\n                }),\n            _errorSteps: (e)=>r(e)\n        }), o;\n    }\n    releaseLock() {\n        if (!Fe(this)) throw De(\"releaseLock\");\n        void 0 !== this._ownerReadableStream && function(e) {\n            W(e);\n            const t = new TypeError(\"Reader was released\");\n            Ie(e, t);\n        }(this);\n    }\n}\nfunction Fe(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_readIntoRequests\") && e instanceof ReadableStreamBYOBReader;\n}\nfunction Ie(e, t) {\n    const r = e._readIntoRequests;\n    e._readIntoRequests = new S, r.forEach((e)=>{\n        e._errorSteps(t);\n    });\n}\nfunction De(e) {\n    return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`);\n}\nfunction $e(e, t) {\n    const { highWaterMark: r } = e;\n    if (void 0 === r) return t;\n    if (ae(r) || r < 0) throw new RangeError(\"Invalid highWaterMark\");\n    return r;\n}\nfunction Me(e) {\n    const { size: t } = e;\n    return t || (()=>1);\n}\nfunction Ye(e, t) {\n    F(e, t);\n    const r = null == e ? void 0 : e.highWaterMark, o = null == e ? void 0 : e.size;\n    return {\n        highWaterMark: void 0 === r ? void 0 : Y(r),\n        size: void 0 === o ? void 0 : Qe(o, `${t} has member 'size' that`)\n    };\n}\nfunction Qe(e, t) {\n    return I(e, t), (t)=>Y(e(t));\n}\nfunction Ne(e, t, r) {\n    return I(e, r), (r)=>w(e, t, [\n            r\n        ]);\n}\nfunction He(e, t, r) {\n    return I(e, r), ()=>w(e, t, []);\n}\nfunction xe(e, t, r) {\n    return I(e, r), (r)=>g(e, t, [\n            r\n        ]);\n}\nfunction Ve(e, t, r) {\n    return I(e, r), (r, o)=>w(e, t, [\n            r,\n            o\n        ]);\n}\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n    cancel: {\n        enumerable: !0\n    },\n    read: {\n        enumerable: !0\n    },\n    releaseLock: {\n        enumerable: !0\n    },\n    closed: {\n        enumerable: !0\n    }\n}), n(ReadableStreamBYOBReader.prototype.cancel, \"cancel\"), n(ReadableStreamBYOBReader.prototype.read, \"read\"), n(ReadableStreamBYOBReader.prototype.releaseLock, \"releaseLock\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(ReadableStreamBYOBReader.prototype, e.toStringTag, {\n    value: \"ReadableStreamBYOBReader\",\n    configurable: !0\n});\nconst Ue = \"function\" == typeof AbortController;\nclass WritableStream {\n    constructor(e = {}, t = {}){\n        void 0 === e ? e = null : D(e, \"First parameter\");\n        const r = Ye(t, \"Second parameter\"), o = function(e, t) {\n            F(e, t);\n            const r = null == e ? void 0 : e.abort, o = null == e ? void 0 : e.close, n = null == e ? void 0 : e.start, a = null == e ? void 0 : e.type, i = null == e ? void 0 : e.write;\n            return {\n                abort: void 0 === r ? void 0 : Ne(r, e, `${t} has member 'abort' that`),\n                close: void 0 === o ? void 0 : He(o, e, `${t} has member 'close' that`),\n                start: void 0 === n ? void 0 : xe(n, e, `${t} has member 'start' that`),\n                write: void 0 === i ? void 0 : Ve(i, e, `${t} has member 'write' that`),\n                type: a\n            };\n        }(e, \"First parameter\");\n        var n;\n        (n = this)._state = \"writable\", n._storedError = void 0, n._writer = void 0, n._writableStreamController = void 0, n._writeRequests = new S, n._inFlightWriteRequest = void 0, n._closeRequest = void 0, n._inFlightCloseRequest = void 0, n._pendingAbortRequest = void 0, n._backpressure = !1;\n        if (void 0 !== o.type) throw new RangeError(\"Invalid type is specified\");\n        const a = Me(r);\n        !function(e, t, r, o) {\n            const n = Object.create(WritableStreamDefaultController.prototype);\n            let a, i, l, s;\n            a = void 0 !== t.start ? ()=>t.start(n) : ()=>{};\n            i = void 0 !== t.write ? (e)=>t.write(e, n) : ()=>c(void 0);\n            l = void 0 !== t.close ? ()=>t.close() : ()=>c(void 0);\n            s = void 0 !== t.abort ? (e)=>t.abort(e) : ()=>c(void 0);\n            !function(e, t, r, o, n, a, i, l) {\n                t._controlledWritableStream = e, e._writableStreamController = t, t._queue = void 0, t._queueTotalSize = void 0, ce(t), t._abortReason = void 0, t._abortController = function() {\n                    if (Ue) return new AbortController;\n                }(), t._started = !1, t._strategySizeAlgorithm = l, t._strategyHWM = i, t._writeAlgorithm = o, t._closeAlgorithm = n, t._abortAlgorithm = a;\n                const s = bt(t);\n                nt(e, s);\n                const u = r();\n                b(c(u), ()=>(t._started = !0, dt(t), null), (r)=>(t._started = !0, Ze(e, r), null));\n            }(e, n, a, i, l, s, r, o);\n        }(this, o, $e(r, 1), a);\n    }\n    get locked() {\n        if (!Ge(this)) throw _t(\"locked\");\n        return Xe(this);\n    }\n    abort(e) {\n        return Ge(this) ? Xe(this) ? d(new TypeError(\"Cannot abort a stream that already has a writer\")) : Je(this, e) : d(_t(\"abort\"));\n    }\n    close() {\n        return Ge(this) ? Xe(this) ? d(new TypeError(\"Cannot close a stream that already has a writer\")) : rt(this) ? d(new TypeError(\"Cannot close an already-closing stream\")) : Ke(this) : d(_t(\"close\"));\n    }\n    getWriter() {\n        if (!Ge(this)) throw _t(\"getWriter\");\n        return new WritableStreamDefaultWriter(this);\n    }\n}\nfunction Ge(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_writableStreamController\") && e instanceof WritableStream;\n}\nfunction Xe(e) {\n    return void 0 !== e._writer;\n}\nfunction Je(e, t) {\n    var r;\n    if (\"closed\" === e._state || \"errored\" === e._state) return c(void 0);\n    e._writableStreamController._abortReason = t, null === (r = e._writableStreamController._abortController) || void 0 === r || r.abort(t);\n    const o = e._state;\n    if (\"closed\" === o || \"errored\" === o) return c(void 0);\n    if (void 0 !== e._pendingAbortRequest) return e._pendingAbortRequest._promise;\n    let n = !1;\n    \"erroring\" === o && (n = !0, t = void 0);\n    const a = u((r, o)=>{\n        e._pendingAbortRequest = {\n            _promise: void 0,\n            _resolve: r,\n            _reject: o,\n            _reason: t,\n            _wasAlreadyErroring: n\n        };\n    });\n    return e._pendingAbortRequest._promise = a, n || et(e, t), a;\n}\nfunction Ke(e) {\n    const t = e._state;\n    if (\"closed\" === t || \"errored\" === t) return d(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));\n    const r = u((t, r)=>{\n        const o = {\n            _resolve: t,\n            _reject: r\n        };\n        e._closeRequest = o;\n    }), o = e._writer;\n    var n;\n    return void 0 !== o && e._backpressure && \"writable\" === t && Et(o), ue(n = e._writableStreamController, lt, 0), dt(n), r;\n}\nfunction Ze(e, t) {\n    \"writable\" !== e._state ? tt(e) : et(e, t);\n}\nfunction et(e, t) {\n    const r = e._writableStreamController;\n    e._state = \"erroring\", e._storedError = t;\n    const o = e._writer;\n    void 0 !== o && it(o, t), !function(e) {\n        if (void 0 === e._inFlightWriteRequest && void 0 === e._inFlightCloseRequest) return !1;\n        return !0;\n    }(e) && r._started && tt(e);\n}\nfunction tt(e) {\n    e._state = \"errored\", e._writableStreamController[R]();\n    const t = e._storedError;\n    if (e._writeRequests.forEach((e)=>{\n        e._reject(t);\n    }), e._writeRequests = new S, void 0 === e._pendingAbortRequest) return void ot(e);\n    const r = e._pendingAbortRequest;\n    if (e._pendingAbortRequest = void 0, r._wasAlreadyErroring) return r._reject(t), void ot(e);\n    b(e._writableStreamController[v](r._reason), ()=>(r._resolve(), ot(e), null), (t)=>(r._reject(t), ot(e), null));\n}\nfunction rt(e) {\n    return void 0 !== e._closeRequest || void 0 !== e._inFlightCloseRequest;\n}\nfunction ot(e) {\n    void 0 !== e._closeRequest && (e._closeRequest._reject(e._storedError), e._closeRequest = void 0);\n    const t = e._writer;\n    void 0 !== t && St(t, e._storedError);\n}\nfunction nt(e, t) {\n    const r = e._writer;\n    void 0 !== r && t !== e._backpressure && (t ? function(e) {\n        Rt(e);\n    }(r) : Et(r)), e._backpressure = t;\n}\nObject.defineProperties(WritableStream.prototype, {\n    abort: {\n        enumerable: !0\n    },\n    close: {\n        enumerable: !0\n    },\n    getWriter: {\n        enumerable: !0\n    },\n    locked: {\n        enumerable: !0\n    }\n}), n(WritableStream.prototype.abort, \"abort\"), n(WritableStream.prototype.close, \"close\"), n(WritableStream.prototype.getWriter, \"getWriter\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(WritableStream.prototype, e.toStringTag, {\n    value: \"WritableStream\",\n    configurable: !0\n});\nclass WritableStreamDefaultWriter {\n    constructor(e){\n        if ($(e, 1, \"WritableStreamDefaultWriter\"), function(e, t) {\n            if (!Ge(e)) throw new TypeError(`${t} is not a WritableStream.`);\n        }(e, \"First parameter\"), Xe(e)) throw new TypeError(\"This stream has already been locked for exclusive writing by another writer\");\n        this._ownerWritableStream = e, e._writer = this;\n        const t = e._state;\n        if (\"writable\" === t) !rt(e) && e._backpressure ? Rt(this) : qt(this), gt(this);\n        else if (\"erroring\" === t) Tt(this, e._storedError), gt(this);\n        else if (\"closed\" === t) qt(this), gt(r = this), vt(r);\n        else {\n            const t = e._storedError;\n            Tt(this, t), wt(this, t);\n        }\n        var r;\n    }\n    get closed() {\n        return at(this) ? this._closedPromise : d(mt(\"closed\"));\n    }\n    get desiredSize() {\n        if (!at(this)) throw mt(\"desiredSize\");\n        if (void 0 === this._ownerWritableStream) throw yt(\"desiredSize\");\n        return function(e) {\n            const t = e._ownerWritableStream, r = t._state;\n            if (\"errored\" === r || \"erroring\" === r) return null;\n            if (\"closed\" === r) return 0;\n            return ct(t._writableStreamController);\n        }(this);\n    }\n    get ready() {\n        return at(this) ? this._readyPromise : d(mt(\"ready\"));\n    }\n    abort(e) {\n        return at(this) ? void 0 === this._ownerWritableStream ? d(yt(\"abort\")) : function(e, t) {\n            return Je(e._ownerWritableStream, t);\n        }(this, e) : d(mt(\"abort\"));\n    }\n    close() {\n        if (!at(this)) return d(mt(\"close\"));\n        const e = this._ownerWritableStream;\n        return void 0 === e ? d(yt(\"close\")) : rt(e) ? d(new TypeError(\"Cannot close an already-closing stream\")) : Ke(this._ownerWritableStream);\n    }\n    releaseLock() {\n        if (!at(this)) throw mt(\"releaseLock\");\n        void 0 !== this._ownerWritableStream && function(e) {\n            const t = e._ownerWritableStream, r = new TypeError(\"Writer was released and can no longer be used to monitor the stream's closedness\");\n            it(e, r), function(e, t) {\n                \"pending\" === e._closedPromiseState ? St(e, t) : function(e, t) {\n                    wt(e, t);\n                }(e, t);\n            }(e, r), t._writer = void 0, e._ownerWritableStream = void 0;\n        }(this);\n    }\n    write(e) {\n        return at(this) ? void 0 === this._ownerWritableStream ? d(yt(\"write to\")) : function(e, t) {\n            const r = e._ownerWritableStream, o = r._writableStreamController, n = function(e, t) {\n                try {\n                    return e._strategySizeAlgorithm(t);\n                } catch (t) {\n                    return ft(e, t), 1;\n                }\n            }(o, t);\n            if (r !== e._ownerWritableStream) return d(yt(\"write to\"));\n            const a = r._state;\n            if (\"errored\" === a) return d(r._storedError);\n            if (rt(r) || \"closed\" === a) return d(new TypeError(\"The stream is closing or closed and cannot be written to\"));\n            if (\"erroring\" === a) return d(r._storedError);\n            const i = function(e) {\n                return u((t, r)=>{\n                    const o = {\n                        _resolve: t,\n                        _reject: r\n                    };\n                    e._writeRequests.push(o);\n                });\n            }(r);\n            return function(e, t, r) {\n                try {\n                    ue(e, t, r);\n                } catch (t) {\n                    return void ft(e, t);\n                }\n                const o = e._controlledWritableStream;\n                if (!rt(o) && \"writable\" === o._state) {\n                    nt(o, bt(e));\n                }\n                dt(e);\n            }(o, t, n), i;\n        }(this, e) : d(mt(\"write\"));\n    }\n}\nfunction at(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_ownerWritableStream\") && e instanceof WritableStreamDefaultWriter;\n}\nfunction it(e, t) {\n    \"pending\" === e._readyPromiseState ? Ct(e, t) : function(e, t) {\n        Tt(e, t);\n    }(e, t);\n}\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n    abort: {\n        enumerable: !0\n    },\n    close: {\n        enumerable: !0\n    },\n    releaseLock: {\n        enumerable: !0\n    },\n    write: {\n        enumerable: !0\n    },\n    closed: {\n        enumerable: !0\n    },\n    desiredSize: {\n        enumerable: !0\n    },\n    ready: {\n        enumerable: !0\n    }\n}), n(WritableStreamDefaultWriter.prototype.abort, \"abort\"), n(WritableStreamDefaultWriter.prototype.close, \"close\"), n(WritableStreamDefaultWriter.prototype.releaseLock, \"releaseLock\"), n(WritableStreamDefaultWriter.prototype.write, \"write\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(WritableStreamDefaultWriter.prototype, e.toStringTag, {\n    value: \"WritableStreamDefaultWriter\",\n    configurable: !0\n});\nconst lt = {};\nclass WritableStreamDefaultController {\n    constructor(){\n        throw new TypeError(\"Illegal constructor\");\n    }\n    get abortReason() {\n        if (!st(this)) throw pt(\"abortReason\");\n        return this._abortReason;\n    }\n    get signal() {\n        if (!st(this)) throw pt(\"signal\");\n        if (void 0 === this._abortController) throw new TypeError(\"WritableStreamDefaultController.prototype.signal is not supported\");\n        return this._abortController.signal;\n    }\n    error(e) {\n        if (!st(this)) throw pt(\"error\");\n        \"writable\" === this._controlledWritableStream._state && ht(this, e);\n    }\n    [v](e) {\n        const t = this._abortAlgorithm(e);\n        return ut(this), t;\n    }\n    [R]() {\n        ce(this);\n    }\n}\nfunction st(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_controlledWritableStream\") && e instanceof WritableStreamDefaultController;\n}\nfunction ut(e) {\n    e._writeAlgorithm = void 0, e._closeAlgorithm = void 0, e._abortAlgorithm = void 0, e._strategySizeAlgorithm = void 0;\n}\nfunction ct(e) {\n    return e._strategyHWM - e._queueTotalSize;\n}\nfunction dt(e) {\n    const t = e._controlledWritableStream;\n    if (!e._started) return;\n    if (void 0 !== t._inFlightWriteRequest) return;\n    if (\"erroring\" === t._state) return void tt(t);\n    if (0 === e._queue.length) return;\n    const r = e._queue.peek().value;\n    r === lt ? function(e) {\n        const t = e._controlledWritableStream;\n        (function(e) {\n            e._inFlightCloseRequest = e._closeRequest, e._closeRequest = void 0;\n        })(t), se(e);\n        const r = e._closeAlgorithm();\n        ut(e), b(r, ()=>((function(e) {\n                e._inFlightCloseRequest._resolve(void 0), e._inFlightCloseRequest = void 0, \"erroring\" === e._state && (e._storedError = void 0, void 0 !== e._pendingAbortRequest && (e._pendingAbortRequest._resolve(), e._pendingAbortRequest = void 0)), e._state = \"closed\";\n                const t = e._writer;\n                void 0 !== t && vt(t);\n            })(t), null), (e)=>((function(e, t) {\n                e._inFlightCloseRequest._reject(t), e._inFlightCloseRequest = void 0, void 0 !== e._pendingAbortRequest && (e._pendingAbortRequest._reject(t), e._pendingAbortRequest = void 0), Ze(e, t);\n            })(t, e), null));\n    }(e) : function(e, t) {\n        const r = e._controlledWritableStream;\n        !function(e) {\n            e._inFlightWriteRequest = e._writeRequests.shift();\n        }(r);\n        b(e._writeAlgorithm(t), ()=>{\n            !function(e) {\n                e._inFlightWriteRequest._resolve(void 0), e._inFlightWriteRequest = void 0;\n            }(r);\n            const t = r._state;\n            if (se(e), !rt(r) && \"writable\" === t) {\n                const t = bt(e);\n                nt(r, t);\n            }\n            return dt(e), null;\n        }, (t)=>(\"writable\" === r._state && ut(e), function(e, t) {\n                e._inFlightWriteRequest._reject(t), e._inFlightWriteRequest = void 0, Ze(e, t);\n            }(r, t), null));\n    }(e, r);\n}\nfunction ft(e, t) {\n    \"writable\" === e._controlledWritableStream._state && ht(e, t);\n}\nfunction bt(e) {\n    return ct(e) <= 0;\n}\nfunction ht(e, t) {\n    const r = e._controlledWritableStream;\n    ut(e), et(r, t);\n}\nfunction _t(e) {\n    return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`);\n}\nfunction pt(e) {\n    return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`);\n}\nfunction mt(e) {\n    return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`);\n}\nfunction yt(e) {\n    return new TypeError(\"Cannot \" + e + \" a stream using a released writer\");\n}\nfunction gt(e) {\n    e._closedPromise = u((t, r)=>{\n        e._closedPromise_resolve = t, e._closedPromise_reject = r, e._closedPromiseState = \"pending\";\n    });\n}\nfunction wt(e, t) {\n    gt(e), St(e, t);\n}\nfunction St(e, t) {\n    void 0 !== e._closedPromise_reject && (m(e._closedPromise), e._closedPromise_reject(t), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0, e._closedPromiseState = \"rejected\");\n}\nfunction vt(e) {\n    void 0 !== e._closedPromise_resolve && (e._closedPromise_resolve(void 0), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0, e._closedPromiseState = \"resolved\");\n}\nfunction Rt(e) {\n    e._readyPromise = u((t, r)=>{\n        e._readyPromise_resolve = t, e._readyPromise_reject = r;\n    }), e._readyPromiseState = \"pending\";\n}\nfunction Tt(e, t) {\n    Rt(e), Ct(e, t);\n}\nfunction qt(e) {\n    Rt(e), Et(e);\n}\nfunction Ct(e, t) {\n    void 0 !== e._readyPromise_reject && (m(e._readyPromise), e._readyPromise_reject(t), e._readyPromise_resolve = void 0, e._readyPromise_reject = void 0, e._readyPromiseState = \"rejected\");\n}\nfunction Et(e) {\n    void 0 !== e._readyPromise_resolve && (e._readyPromise_resolve(void 0), e._readyPromise_resolve = void 0, e._readyPromise_reject = void 0, e._readyPromiseState = \"fulfilled\");\n}\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n    abortReason: {\n        enumerable: !0\n    },\n    signal: {\n        enumerable: !0\n    },\n    error: {\n        enumerable: !0\n    }\n}), \"symbol\" == typeof e.toStringTag && Object.defineProperty(WritableStreamDefaultController.prototype, e.toStringTag, {\n    value: \"WritableStreamDefaultController\",\n    configurable: !0\n});\nconst Pt = \"undefined\" != typeof DOMException ? DOMException : void 0;\nconst Wt = function(e) {\n    if (\"function\" != typeof e && \"object\" != typeof e) return !1;\n    try {\n        return new e, !0;\n    } catch (e) {\n        return !1;\n    }\n}(Pt) ? Pt : function() {\n    const e = function(e, t) {\n        this.message = e || \"\", this.name = t || \"Error\", Error.captureStackTrace && Error.captureStackTrace(this, this.constructor);\n    };\n    return e.prototype = Object.create(Error.prototype), Object.defineProperty(e.prototype, \"constructor\", {\n        value: e,\n        writable: !0,\n        configurable: !0\n    }), e;\n}();\nfunction kt(e, t, r, o, n, a) {\n    const i = e.getReader(), l = t.getWriter();\n    Vt(e) && (e._disturbed = !0);\n    let s, _, g, w = !1, S = !1, v = \"readable\", R = \"writable\", T = !1, q = !1;\n    const C = u((e)=>{\n        g = e;\n    });\n    let E = Promise.resolve(void 0);\n    return u((P, W)=>{\n        let k;\n        function O() {\n            if (w) return;\n            const e = u((e, t)=>{\n                !function r(o) {\n                    o ? e() : f(function() {\n                        if (w) return c(!0);\n                        return f(l.ready, ()=>f(i.read(), (e)=>!!e.done || (E = l.write(e.value), m(E), !1)));\n                    }(), r, t);\n                }(!1);\n            });\n            m(e);\n        }\n        function B() {\n            return v = \"closed\", r ? L() : z(()=>(Ge(t) && (T = rt(t), R = t._state), T || \"closed\" === R ? c(void 0) : \"erroring\" === R || \"errored\" === R ? d(_) : (T = !0, l.close())), !1, void 0), null;\n        }\n        function A(e) {\n            return w || (v = \"errored\", s = e, o ? L(!0, e) : z(()=>l.abort(e), !0, e)), null;\n        }\n        function j(e) {\n            return S || (R = \"errored\", _ = e, n ? L(!0, e) : z(()=>i.cancel(e), !0, e)), null;\n        }\n        if (void 0 !== a && (k = ()=>{\n            const e = void 0 !== a.reason ? a.reason : new Wt(\"Aborted\", \"AbortError\"), t = [];\n            o || t.push(()=>\"writable\" === R ? l.abort(e) : c(void 0)), n || t.push(()=>\"readable\" === v ? i.cancel(e) : c(void 0)), z(()=>Promise.all(t.map((e)=>e())), !0, e);\n        }, a.aborted ? k() : a.addEventListener(\"abort\", k)), Vt(e) && (v = e._state, s = e._storedError), Ge(t) && (R = t._state, _ = t._storedError, T = rt(t)), Vt(e) && Ge(t) && (q = !0, g()), \"errored\" === v) A(s);\n        else if (\"erroring\" === R || \"errored\" === R) j(_);\n        else if (\"closed\" === v) B();\n        else if (T || \"closed\" === R) {\n            const e = new TypeError(\"the destination writable stream closed before all data could be piped to it\");\n            n ? L(!0, e) : z(()=>i.cancel(e), !0, e);\n        }\n        function z(e, t, r) {\n            function o() {\n                return \"writable\" !== R || T ? n() : h(function() {\n                    let e;\n                    return c(function t() {\n                        if (e !== E) return e = E, p(E, t, t);\n                    }());\n                }(), n), null;\n            }\n            function n() {\n                return e ? b(e(), ()=>F(t, r), (e)=>F(!0, e)) : F(t, r), null;\n            }\n            w || (w = !0, q ? o() : h(C, o));\n        }\n        function L(e, t) {\n            z(void 0, e, t);\n        }\n        function F(e, t) {\n            return S = !0, l.releaseLock(), i.releaseLock(), void 0 !== a && a.removeEventListener(\"abort\", k), e ? W(t) : P(void 0), null;\n        }\n        w || (b(i.closed, B, A), b(l.closed, function() {\n            return S || (R = \"closed\"), null;\n        }, j)), q ? O() : y(()=>{\n            q = !0, g(), O();\n        });\n    });\n}\nfunction Ot(e, t) {\n    return function(e) {\n        try {\n            return e.getReader({\n                mode: \"byob\"\n            }).releaseLock(), !0;\n        } catch (e) {\n            return !1;\n        }\n    }(e) ? function(e) {\n        let t, r, o, n, a, i = e.getReader(), l = !1, s = !1, d = !1, f = !1, h = !1, p = !1;\n        const m = u((e)=>{\n            a = e;\n        });\n        function y(e) {\n            _(e.closed, (t)=>(e !== i || (o.error(t), n.error(t), h && p || a(void 0)), null));\n        }\n        function g() {\n            l && (i.releaseLock(), i = e.getReader(), y(i), l = !1), b(i.read(), (e)=>{\n                var t, r;\n                if (d = !1, f = !1, e.done) return h || o.close(), p || n.close(), null === (t = o.byobRequest) || void 0 === t || t.respond(0), null === (r = n.byobRequest) || void 0 === r || r.respond(0), h && p || a(void 0), null;\n                const l = e.value, u = l;\n                let c = l;\n                if (!h && !p) try {\n                    c = le(l);\n                } catch (e) {\n                    return o.error(e), n.error(e), a(i.cancel(e)), null;\n                }\n                return h || o.enqueue(u), p || n.enqueue(c), s = !1, d ? S() : f && v(), null;\n            }, ()=>(s = !1, null));\n        }\n        function w(t, r) {\n            l || (i.releaseLock(), i = e.getReader({\n                mode: \"byob\"\n            }), y(i), l = !0);\n            const u = r ? n : o, c = r ? o : n;\n            b(i.read(t), (e)=>{\n                var t;\n                d = !1, f = !1;\n                const o = r ? p : h, n = r ? h : p;\n                if (e.done) {\n                    o || u.close(), n || c.close();\n                    const r = e.value;\n                    return void 0 !== r && (o || u.byobRequest.respondWithNewView(r), n || null === (t = c.byobRequest) || void 0 === t || t.respond(0)), o && n || a(void 0), null;\n                }\n                const l = e.value;\n                if (n) o || u.byobRequest.respondWithNewView(l);\n                else {\n                    let e;\n                    try {\n                        e = le(l);\n                    } catch (e) {\n                        return u.error(e), c.error(e), a(i.cancel(e)), null;\n                    }\n                    o || u.byobRequest.respondWithNewView(l), c.enqueue(e);\n                }\n                return s = !1, d ? S() : f && v(), null;\n            }, ()=>(s = !1, null));\n        }\n        function S() {\n            if (s) return d = !0, c(void 0);\n            s = !0;\n            const e = o.byobRequest;\n            return null === e ? g() : w(e.view, !1), c(void 0);\n        }\n        function v() {\n            if (s) return f = !0, c(void 0);\n            s = !0;\n            const e = n.byobRequest;\n            return null === e ? g() : w(e.view, !0), c(void 0);\n        }\n        function R(e) {\n            if (h = !0, t = e, p) {\n                const e = [\n                    t,\n                    r\n                ], o = i.cancel(e);\n                a(o);\n            }\n            return m;\n        }\n        function T(e) {\n            if (p = !0, r = e, h) {\n                const e = [\n                    t,\n                    r\n                ], o = i.cancel(e);\n                a(o);\n            }\n            return m;\n        }\n        const q = new ReadableStream({\n            type: \"bytes\",\n            start (e) {\n                o = e;\n            },\n            pull: S,\n            cancel: R\n        }), C = new ReadableStream({\n            type: \"bytes\",\n            start (e) {\n                n = e;\n            },\n            pull: v,\n            cancel: T\n        });\n        return y(i), [\n            q,\n            C\n        ];\n    }(e) : function(e, t) {\n        const r = e.getReader();\n        let o, n, a, i, l, s = !1, d = !1, f = !1, h = !1;\n        const p = u((e)=>{\n            l = e;\n        });\n        function m() {\n            return s ? (d = !0, c(void 0)) : (s = !0, b(r.read(), (e)=>{\n                if (d = !1, e.done) return f || a.close(), h || i.close(), f && h || l(void 0), null;\n                const t = e.value, r = t, o = t;\n                return f || a.enqueue(r), h || i.enqueue(o), s = !1, d && m(), null;\n            }, ()=>(s = !1, null)), c(void 0));\n        }\n        function y(e) {\n            if (f = !0, o = e, h) {\n                const e = [\n                    o,\n                    n\n                ], t = r.cancel(e);\n                l(t);\n            }\n            return p;\n        }\n        function g(e) {\n            if (h = !0, n = e, f) {\n                const e = [\n                    o,\n                    n\n                ], t = r.cancel(e);\n                l(t);\n            }\n            return p;\n        }\n        const w = new ReadableStream({\n            start (e) {\n                a = e;\n            },\n            pull: m,\n            cancel: y\n        }), S = new ReadableStream({\n            start (e) {\n                i = e;\n            },\n            pull: m,\n            cancel: g\n        });\n        return _(r.closed, (e)=>(a.error(e), i.error(e), f && h || l(void 0), null)), [\n            w,\n            S\n        ];\n    }(e);\n}\nclass ReadableStreamDefaultController {\n    constructor(){\n        throw new TypeError(\"Illegal constructor\");\n    }\n    get desiredSize() {\n        if (!Bt(this)) throw Dt(\"desiredSize\");\n        return Lt(this);\n    }\n    close() {\n        if (!Bt(this)) throw Dt(\"close\");\n        if (!Ft(this)) throw new TypeError(\"The stream is not in a state that permits close\");\n        !function(e) {\n            if (!Ft(e)) return;\n            const t = e._controlledReadableStream;\n            e._closeRequested = !0, 0 === e._queue.length && (jt(e), Xt(t));\n        }(this);\n    }\n    enqueue(e) {\n        if (!Bt(this)) throw Dt(\"enqueue\");\n        if (!Ft(this)) throw new TypeError(\"The stream is not in a state that permits enqueue\");\n        return function(e, t) {\n            if (!Ft(e)) return;\n            const r = e._controlledReadableStream;\n            if (Ut(r) && X(r) > 0) G(r, t, !1);\n            else {\n                let r;\n                try {\n                    r = e._strategySizeAlgorithm(t);\n                } catch (t) {\n                    throw zt(e, t), t;\n                }\n                try {\n                    ue(e, t, r);\n                } catch (t) {\n                    throw zt(e, t), t;\n                }\n            }\n            At(e);\n        }(this, e);\n    }\n    error(e) {\n        if (!Bt(this)) throw Dt(\"error\");\n        zt(this, e);\n    }\n    [T](e) {\n        ce(this);\n        const t = this._cancelAlgorithm(e);\n        return jt(this), t;\n    }\n    [q](e) {\n        const t = this._controlledReadableStream;\n        if (this._queue.length > 0) {\n            const r = se(this);\n            this._closeRequested && 0 === this._queue.length ? (jt(this), Xt(t)) : At(this), e._chunkSteps(r);\n        } else U(t, e), At(this);\n    }\n    [C]() {}\n}\nfunction Bt(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_controlledReadableStream\") && e instanceof ReadableStreamDefaultController;\n}\nfunction At(e) {\n    const t = function(e) {\n        const t = e._controlledReadableStream;\n        if (!Ft(e)) return !1;\n        if (!e._started) return !1;\n        if (Ut(t) && X(t) > 0) return !0;\n        if (Lt(e) > 0) return !0;\n        return !1;\n    }(e);\n    if (!t) return;\n    if (e._pulling) return void (e._pullAgain = !0);\n    e._pulling = !0;\n    b(e._pullAlgorithm(), ()=>(e._pulling = !1, e._pullAgain && (e._pullAgain = !1, At(e)), null), (t)=>(zt(e, t), null));\n}\nfunction jt(e) {\n    e._pullAlgorithm = void 0, e._cancelAlgorithm = void 0, e._strategySizeAlgorithm = void 0;\n}\nfunction zt(e, t) {\n    const r = e._controlledReadableStream;\n    \"readable\" === r._state && (ce(e), jt(e), Jt(r, t));\n}\nfunction Lt(e) {\n    const t = e._controlledReadableStream._state;\n    return \"errored\" === t ? null : \"closed\" === t ? 0 : e._strategyHWM - e._queueTotalSize;\n}\nfunction Ft(e) {\n    return !e._closeRequested && \"readable\" === e._controlledReadableStream._state;\n}\nfunction It(e, t, r, o) {\n    const n = Object.create(ReadableStreamDefaultController.prototype);\n    let a, i, l;\n    a = void 0 !== t.start ? ()=>t.start(n) : ()=>{}, i = void 0 !== t.pull ? ()=>t.pull(n) : ()=>c(void 0), l = void 0 !== t.cancel ? (e)=>t.cancel(e) : ()=>c(void 0), function(e, t, r, o, n, a, i) {\n        t._controlledReadableStream = e, t._queue = void 0, t._queueTotalSize = void 0, ce(t), t._started = !1, t._closeRequested = !1, t._pullAgain = !1, t._pulling = !1, t._strategySizeAlgorithm = i, t._strategyHWM = a, t._pullAlgorithm = o, t._cancelAlgorithm = n, e._readableStreamController = t, b(c(r()), ()=>(t._started = !0, At(t), null), (e)=>(zt(t, e), null));\n    }(e, n, a, i, l, r, o);\n}\nfunction Dt(e) {\n    return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`);\n}\nfunction $t(e, t, r) {\n    return I(e, r), (r)=>w(e, t, [\n            r\n        ]);\n}\nfunction Mt(e, t, r) {\n    return I(e, r), (r)=>w(e, t, [\n            r\n        ]);\n}\nfunction Yt(e, t, r) {\n    return I(e, r), (r)=>g(e, t, [\n            r\n        ]);\n}\nfunction Qt(e, t) {\n    if (\"bytes\" !== (e = `${e}`)) throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);\n    return e;\n}\nfunction Nt(e, t) {\n    if (\"byob\" !== (e = `${e}`)) throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);\n    return e;\n}\nfunction Ht(e, t) {\n    F(e, t);\n    const r = null == e ? void 0 : e.preventAbort, o = null == e ? void 0 : e.preventCancel, n = null == e ? void 0 : e.preventClose, a = null == e ? void 0 : e.signal;\n    return void 0 !== a && function(e, t) {\n        if (!function(e) {\n            if (\"object\" != typeof e || null === e) return !1;\n            try {\n                return \"boolean\" == typeof e.aborted;\n            } catch (e) {\n                return !1;\n            }\n        }(e)) throw new TypeError(`${t} is not an AbortSignal.`);\n    }(a, `${t} has member 'signal' that`), {\n        preventAbort: Boolean(r),\n        preventCancel: Boolean(o),\n        preventClose: Boolean(n),\n        signal: a\n    };\n}\nfunction xt(e, t) {\n    F(e, t);\n    const r = null == e ? void 0 : e.readable;\n    M(r, \"readable\", \"ReadableWritablePair\"), function(e, t) {\n        if (!H(e)) throw new TypeError(`${t} is not a ReadableStream.`);\n    }(r, `${t} has member 'readable' that`);\n    const o = null == e ? void 0 : e.writable;\n    return M(o, \"writable\", \"ReadableWritablePair\"), function(e, t) {\n        if (!x(e)) throw new TypeError(`${t} is not a WritableStream.`);\n    }(o, `${t} has member 'writable' that`), {\n        readable: r,\n        writable: o\n    };\n}\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n    close: {\n        enumerable: !0\n    },\n    enqueue: {\n        enumerable: !0\n    },\n    error: {\n        enumerable: !0\n    },\n    desiredSize: {\n        enumerable: !0\n    }\n}), n(ReadableStreamDefaultController.prototype.close, \"close\"), n(ReadableStreamDefaultController.prototype.enqueue, \"enqueue\"), n(ReadableStreamDefaultController.prototype.error, \"error\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(ReadableStreamDefaultController.prototype, e.toStringTag, {\n    value: \"ReadableStreamDefaultController\",\n    configurable: !0\n});\nclass ReadableStream {\n    constructor(e = {}, t = {}){\n        void 0 === e ? e = null : D(e, \"First parameter\");\n        const r = Ye(t, \"Second parameter\"), o = function(e, t) {\n            F(e, t);\n            const r = e, o = null == r ? void 0 : r.autoAllocateChunkSize, n = null == r ? void 0 : r.cancel, a = null == r ? void 0 : r.pull, i = null == r ? void 0 : r.start, l = null == r ? void 0 : r.type;\n            return {\n                autoAllocateChunkSize: void 0 === o ? void 0 : N(o, `${t} has member 'autoAllocateChunkSize' that`),\n                cancel: void 0 === n ? void 0 : $t(n, r, `${t} has member 'cancel' that`),\n                pull: void 0 === a ? void 0 : Mt(a, r, `${t} has member 'pull' that`),\n                start: void 0 === i ? void 0 : Yt(i, r, `${t} has member 'start' that`),\n                type: void 0 === l ? void 0 : Qt(l, `${t} has member 'type' that`)\n            };\n        }(e, \"First parameter\");\n        var n;\n        if ((n = this)._state = \"readable\", n._reader = void 0, n._storedError = void 0, n._disturbed = !1, \"bytes\" === o.type) {\n            if (void 0 !== r.size) throw new RangeError(\"The strategy for a byte stream cannot have a size function\");\n            Oe(this, o, $e(r, 0));\n        } else {\n            const e = Me(r);\n            It(this, o, $e(r, 1), e);\n        }\n    }\n    get locked() {\n        if (!Vt(this)) throw Kt(\"locked\");\n        return Ut(this);\n    }\n    cancel(e) {\n        return Vt(this) ? Ut(this) ? d(new TypeError(\"Cannot cancel a stream that already has a reader\")) : Gt(this, e) : d(Kt(\"cancel\"));\n    }\n    getReader(e) {\n        if (!Vt(this)) throw Kt(\"getReader\");\n        return void 0 === function(e, t) {\n            F(e, t);\n            const r = null == e ? void 0 : e.mode;\n            return {\n                mode: void 0 === r ? void 0 : Nt(r, `${t} has member 'mode' that`)\n            };\n        }(e, \"First parameter\").mode ? new ReadableStreamDefaultReader(this) : function(e) {\n            return new ReadableStreamBYOBReader(e);\n        }(this);\n    }\n    pipeThrough(e, t = {}) {\n        if (!H(this)) throw Kt(\"pipeThrough\");\n        $(e, 1, \"pipeThrough\");\n        const r = xt(e, \"First parameter\"), o = Ht(t, \"Second parameter\");\n        if (this.locked) throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream\");\n        if (r.writable.locked) throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream\");\n        return m(kt(this, r.writable, o.preventClose, o.preventAbort, o.preventCancel, o.signal)), r.readable;\n    }\n    pipeTo(e, t = {}) {\n        if (!H(this)) return d(Kt(\"pipeTo\"));\n        if (void 0 === e) return d(\"Parameter 1 is required in 'pipeTo'.\");\n        if (!x(e)) return d(new TypeError(\"ReadableStream.prototype.pipeTo's first argument must be a WritableStream\"));\n        let r;\n        try {\n            r = Ht(t, \"Second parameter\");\n        } catch (e) {\n            return d(e);\n        }\n        return this.locked ? d(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream\")) : e.locked ? d(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream\")) : kt(this, e, r.preventClose, r.preventAbort, r.preventCancel, r.signal);\n    }\n    tee() {\n        if (!H(this)) throw Kt(\"tee\");\n        if (this.locked) throw new TypeError(\"Cannot tee a stream that already has a reader\");\n        return Ot(this);\n    }\n    values(e) {\n        if (!H(this)) throw Kt(\"values\");\n        return function(e, t) {\n            const r = e.getReader(), o = new te(r, t), n = Object.create(re);\n            return n._asyncIteratorImpl = o, n;\n        }(this, function(e, t) {\n            F(e, t);\n            const r = null == e ? void 0 : e.preventCancel;\n            return {\n                preventCancel: Boolean(r)\n            };\n        }(e, \"First parameter\").preventCancel);\n    }\n}\nfunction Vt(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_readableStreamController\") && e instanceof ReadableStream;\n}\nfunction Ut(e) {\n    return void 0 !== e._reader;\n}\nfunction Gt(e, r) {\n    if (e._disturbed = !0, \"closed\" === e._state) return c(void 0);\n    if (\"errored\" === e._state) return d(e._storedError);\n    Xt(e);\n    const o = e._reader;\n    if (void 0 !== o && Fe(o)) {\n        const e = o._readIntoRequests;\n        o._readIntoRequests = new S, e.forEach((e)=>{\n            e._closeSteps(void 0);\n        });\n    }\n    return p(e._readableStreamController[T](r), t);\n}\nfunction Xt(e) {\n    e._state = \"closed\";\n    const t = e._reader;\n    if (void 0 !== t && (j(t), K(t))) {\n        const e = t._readRequests;\n        t._readRequests = new S, e.forEach((e)=>{\n            e._closeSteps();\n        });\n    }\n}\nfunction Jt(e, t) {\n    e._state = \"errored\", e._storedError = t;\n    const r = e._reader;\n    void 0 !== r && (A(r, t), K(r) ? Z(r, t) : Ie(r, t));\n}\nfunction Kt(e) {\n    return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`);\n}\nfunction Zt(e, t) {\n    F(e, t);\n    const r = null == e ? void 0 : e.highWaterMark;\n    return M(r, \"highWaterMark\", \"QueuingStrategyInit\"), {\n        highWaterMark: Y(r)\n    };\n}\nObject.defineProperties(ReadableStream.prototype, {\n    cancel: {\n        enumerable: !0\n    },\n    getReader: {\n        enumerable: !0\n    },\n    pipeThrough: {\n        enumerable: !0\n    },\n    pipeTo: {\n        enumerable: !0\n    },\n    tee: {\n        enumerable: !0\n    },\n    values: {\n        enumerable: !0\n    },\n    locked: {\n        enumerable: !0\n    }\n}), n(ReadableStream.prototype.cancel, \"cancel\"), n(ReadableStream.prototype.getReader, \"getReader\"), n(ReadableStream.prototype.pipeThrough, \"pipeThrough\"), n(ReadableStream.prototype.pipeTo, \"pipeTo\"), n(ReadableStream.prototype.tee, \"tee\"), n(ReadableStream.prototype.values, \"values\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(ReadableStream.prototype, e.toStringTag, {\n    value: \"ReadableStream\",\n    configurable: !0\n}), \"symbol\" == typeof e.asyncIterator && Object.defineProperty(ReadableStream.prototype, e.asyncIterator, {\n    value: ReadableStream.prototype.values,\n    writable: !0,\n    configurable: !0\n});\nconst er = (e)=>e.byteLength;\nn(er, \"size\");\nclass ByteLengthQueuingStrategy {\n    constructor(e){\n        $(e, 1, \"ByteLengthQueuingStrategy\"), e = Zt(e, \"First parameter\"), this._byteLengthQueuingStrategyHighWaterMark = e.highWaterMark;\n    }\n    get highWaterMark() {\n        if (!rr(this)) throw tr(\"highWaterMark\");\n        return this._byteLengthQueuingStrategyHighWaterMark;\n    }\n    get size() {\n        if (!rr(this)) throw tr(\"size\");\n        return er;\n    }\n}\nfunction tr(e) {\n    return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`);\n}\nfunction rr(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_byteLengthQueuingStrategyHighWaterMark\") && e instanceof ByteLengthQueuingStrategy;\n}\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n    highWaterMark: {\n        enumerable: !0\n    },\n    size: {\n        enumerable: !0\n    }\n}), \"symbol\" == typeof e.toStringTag && Object.defineProperty(ByteLengthQueuingStrategy.prototype, e.toStringTag, {\n    value: \"ByteLengthQueuingStrategy\",\n    configurable: !0\n});\nconst or = ()=>1;\nn(or, \"size\");\nclass CountQueuingStrategy {\n    constructor(e){\n        $(e, 1, \"CountQueuingStrategy\"), e = Zt(e, \"First parameter\"), this._countQueuingStrategyHighWaterMark = e.highWaterMark;\n    }\n    get highWaterMark() {\n        if (!ar(this)) throw nr(\"highWaterMark\");\n        return this._countQueuingStrategyHighWaterMark;\n    }\n    get size() {\n        if (!ar(this)) throw nr(\"size\");\n        return or;\n    }\n}\nfunction nr(e) {\n    return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`);\n}\nfunction ar(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_countQueuingStrategyHighWaterMark\") && e instanceof CountQueuingStrategy;\n}\nfunction ir(e, t, r) {\n    return I(e, r), (r)=>w(e, t, [\n            r\n        ]);\n}\nfunction lr(e, t, r) {\n    return I(e, r), (r)=>g(e, t, [\n            r\n        ]);\n}\nfunction sr(e, t, r) {\n    return I(e, r), (r, o)=>w(e, t, [\n            r,\n            o\n        ]);\n}\nObject.defineProperties(CountQueuingStrategy.prototype, {\n    highWaterMark: {\n        enumerable: !0\n    },\n    size: {\n        enumerable: !0\n    }\n}), \"symbol\" == typeof e.toStringTag && Object.defineProperty(CountQueuingStrategy.prototype, e.toStringTag, {\n    value: \"CountQueuingStrategy\",\n    configurable: !0\n});\nclass TransformStream {\n    constructor(e = {}, t = {}, r = {}){\n        void 0 === e && (e = null);\n        const o = Ye(t, \"Second parameter\"), n = Ye(r, \"Third parameter\"), a = function(e, t) {\n            F(e, t);\n            const r = null == e ? void 0 : e.flush, o = null == e ? void 0 : e.readableType, n = null == e ? void 0 : e.start, a = null == e ? void 0 : e.transform, i = null == e ? void 0 : e.writableType;\n            return {\n                flush: void 0 === r ? void 0 : ir(r, e, `${t} has member 'flush' that`),\n                readableType: o,\n                start: void 0 === n ? void 0 : lr(n, e, `${t} has member 'start' that`),\n                transform: void 0 === a ? void 0 : sr(a, e, `${t} has member 'transform' that`),\n                writableType: i\n            };\n        }(e, \"First parameter\");\n        if (void 0 !== a.readableType) throw new RangeError(\"Invalid readableType specified\");\n        if (void 0 !== a.writableType) throw new RangeError(\"Invalid writableType specified\");\n        const i = $e(n, 0), l = Me(n), s = $e(o, 1), f = Me(o);\n        let b;\n        !function(e, t, r, o, n, a) {\n            function i() {\n                return t;\n            }\n            function l(t) {\n                return function(e, t) {\n                    const r = e._transformStreamController;\n                    if (e._backpressure) {\n                        return p(e._backpressureChangePromise, ()=>{\n                            if (\"erroring\" === (Ge(e._writable) ? e._writable._state : e._writableState)) throw Ge(e._writable) ? e._writable._storedError : e._writableStoredError;\n                            return pr(r, t);\n                        });\n                    }\n                    return pr(r, t);\n                }(e, t);\n            }\n            function s(t) {\n                return function(e, t) {\n                    return cr(e, t), c(void 0);\n                }(e, t);\n            }\n            function u() {\n                return function(e) {\n                    const t = e._transformStreamController, r = t._flushAlgorithm();\n                    return hr(t), p(r, ()=>{\n                        if (\"errored\" === e._readableState) throw e._readableStoredError;\n                        gr(e) && wr(e);\n                    }, (t)=>{\n                        throw cr(e, t), e._readableStoredError;\n                    });\n                }(e);\n            }\n            function d() {\n                return function(e) {\n                    return fr(e, !1), e._backpressureChangePromise;\n                }(e);\n            }\n            function f(t) {\n                return dr(e, t), c(void 0);\n            }\n            e._writableState = \"writable\", e._writableStoredError = void 0, e._writableHasInFlightOperation = !1, e._writableStarted = !1, e._writable = function(e, t, r, o, n, a, i) {\n                return new WritableStream({\n                    start (r) {\n                        e._writableController = r;\n                        try {\n                            const t = r.signal;\n                            void 0 !== t && t.addEventListener(\"abort\", ()=>{\n                                \"writable\" === e._writableState && (e._writableState = \"erroring\", t.reason && (e._writableStoredError = t.reason));\n                            });\n                        } catch (e) {}\n                        return p(t(), ()=>(e._writableStarted = !0, Cr(e), null), (t)=>{\n                            throw e._writableStarted = !0, Rr(e, t), t;\n                        });\n                    },\n                    write: (t)=>((function(e) {\n                            e._writableHasInFlightOperation = !0;\n                        })(e), p(r(t), ()=>((function(e) {\n                                e._writableHasInFlightOperation = !1;\n                            })(e), Cr(e), null), (t)=>{\n                            throw function(e, t) {\n                                e._writableHasInFlightOperation = !1, Rr(e, t);\n                            }(e, t), t;\n                        })),\n                    close: ()=>((function(e) {\n                            e._writableHasInFlightOperation = !0;\n                        })(e), p(o(), ()=>((function(e) {\n                                e._writableHasInFlightOperation = !1;\n                                \"erroring\" === e._writableState && (e._writableStoredError = void 0);\n                                e._writableState = \"closed\";\n                            })(e), null), (t)=>{\n                            throw function(e, t) {\n                                e._writableHasInFlightOperation = !1, e._writableState, Rr(e, t);\n                            }(e, t), t;\n                        })),\n                    abort: (t)=>(e._writableState = \"errored\", e._writableStoredError = t, n(t))\n                }, {\n                    highWaterMark: a,\n                    size: i\n                });\n            }(e, i, l, u, s, r, o), e._readableState = \"readable\", e._readableStoredError = void 0, e._readableCloseRequested = !1, e._readablePulling = !1, e._readable = function(e, t, r, o, n, a) {\n                return new ReadableStream({\n                    start: (r)=>(e._readableController = r, t().catch((t)=>{\n                            Sr(e, t);\n                        })),\n                    pull: ()=>(e._readablePulling = !0, r().catch((t)=>{\n                            Sr(e, t);\n                        })),\n                    cancel: (t)=>(e._readableState = \"closed\", o(t))\n                }, {\n                    highWaterMark: n,\n                    size: a\n                });\n            }(e, i, d, f, n, a), e._backpressure = void 0, e._backpressureChangePromise = void 0, e._backpressureChangePromise_resolve = void 0, fr(e, !0), e._transformStreamController = void 0;\n        }(this, u((e)=>{\n            b = e;\n        }), s, f, i, l), function(e, t) {\n            const r = Object.create(TransformStreamDefaultController.prototype);\n            let o, n;\n            o = void 0 !== t.transform ? (e)=>t.transform(e, r) : (e)=>{\n                try {\n                    return _r(r, e), c(void 0);\n                } catch (e) {\n                    return d(e);\n                }\n            };\n            n = void 0 !== t.flush ? ()=>t.flush(r) : ()=>c(void 0);\n            !function(e, t, r, o) {\n                t._controlledTransformStream = e, e._transformStreamController = t, t._transformAlgorithm = r, t._flushAlgorithm = o;\n            }(e, r, o, n);\n        }(this, a), void 0 !== a.start ? b(a.start(this._transformStreamController)) : b(void 0);\n    }\n    get readable() {\n        if (!ur(this)) throw yr(\"readable\");\n        return this._readable;\n    }\n    get writable() {\n        if (!ur(this)) throw yr(\"writable\");\n        return this._writable;\n    }\n}\nfunction ur(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_transformStreamController\") && e instanceof TransformStream;\n}\nfunction cr(e, t) {\n    Sr(e, t), dr(e, t);\n}\nfunction dr(e, t) {\n    hr(e._transformStreamController), function(e, t) {\n        e._writableController.error(t);\n        \"writable\" === e._writableState && Tr(e, t);\n    }(e, t), e._backpressure && fr(e, !1);\n}\nfunction fr(e, t) {\n    void 0 !== e._backpressureChangePromise && e._backpressureChangePromise_resolve(), e._backpressureChangePromise = u((t)=>{\n        e._backpressureChangePromise_resolve = t;\n    }), e._backpressure = t;\n}\nObject.defineProperties(TransformStream.prototype, {\n    readable: {\n        enumerable: !0\n    },\n    writable: {\n        enumerable: !0\n    }\n}), \"symbol\" == typeof e.toStringTag && Object.defineProperty(TransformStream.prototype, e.toStringTag, {\n    value: \"TransformStream\",\n    configurable: !0\n});\nclass TransformStreamDefaultController {\n    constructor(){\n        throw new TypeError(\"Illegal constructor\");\n    }\n    get desiredSize() {\n        if (!br(this)) throw mr(\"desiredSize\");\n        return vr(this._controlledTransformStream);\n    }\n    enqueue(e) {\n        if (!br(this)) throw mr(\"enqueue\");\n        _r(this, e);\n    }\n    error(e) {\n        if (!br(this)) throw mr(\"error\");\n        var t;\n        t = e, cr(this._controlledTransformStream, t);\n    }\n    terminate() {\n        if (!br(this)) throw mr(\"terminate\");\n        !function(e) {\n            const t = e._controlledTransformStream;\n            gr(t) && wr(t);\n            const r = new TypeError(\"TransformStream terminated\");\n            dr(t, r);\n        }(this);\n    }\n}\nfunction br(e) {\n    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, \"_controlledTransformStream\") && e instanceof TransformStreamDefaultController;\n}\nfunction hr(e) {\n    e._transformAlgorithm = void 0, e._flushAlgorithm = void 0;\n}\nfunction _r(e, t) {\n    const r = e._controlledTransformStream;\n    if (!gr(r)) throw new TypeError(\"Readable side is not in a state that permits enqueue\");\n    try {\n        !function(e, t) {\n            e._readablePulling = !1;\n            try {\n                e._readableController.enqueue(t);\n            } catch (t) {\n                throw Sr(e, t), t;\n            }\n        }(r, t);\n    } catch (e) {\n        throw dr(r, e), r._readableStoredError;\n    }\n    const o = function(e) {\n        return !function(e) {\n            if (!gr(e)) return !1;\n            if (e._readablePulling) return !0;\n            if (vr(e) > 0) return !0;\n            return !1;\n        }(e);\n    }(r);\n    o !== r._backpressure && fr(r, !0);\n}\nfunction pr(e, t) {\n    return p(e._transformAlgorithm(t), void 0, (t)=>{\n        throw cr(e._controlledTransformStream, t), t;\n    });\n}\nfunction mr(e) {\n    return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`);\n}\nfunction yr(e) {\n    return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`);\n}\nfunction gr(e) {\n    return !e._readableCloseRequested && \"readable\" === e._readableState;\n}\nfunction wr(e) {\n    e._readableState = \"closed\", e._readableCloseRequested = !0, e._readableController.close();\n}\nfunction Sr(e, t) {\n    \"readable\" === e._readableState && (e._readableState = \"errored\", e._readableStoredError = t), e._readableController.error(t);\n}\nfunction vr(e) {\n    return e._readableController.desiredSize;\n}\nfunction Rr(e, t) {\n    \"writable\" !== e._writableState ? qr(e) : Tr(e, t);\n}\nfunction Tr(e, t) {\n    e._writableState = \"erroring\", e._writableStoredError = t, !function(e) {\n        return e._writableHasInFlightOperation;\n    }(e) && e._writableStarted && qr(e);\n}\nfunction qr(e) {\n    e._writableState = \"errored\";\n}\nfunction Cr(e) {\n    \"erroring\" === e._writableState && qr(e);\n}\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n    enqueue: {\n        enumerable: !0\n    },\n    error: {\n        enumerable: !0\n    },\n    terminate: {\n        enumerable: !0\n    },\n    desiredSize: {\n        enumerable: !0\n    }\n}), n(TransformStreamDefaultController.prototype.enqueue, \"enqueue\"), n(TransformStreamDefaultController.prototype.error, \"error\"), n(TransformStreamDefaultController.prototype.terminate, \"terminate\"), \"symbol\" == typeof e.toStringTag && Object.defineProperty(TransformStreamDefaultController.prototype, e.toStringTag, {\n    value: \"TransformStreamDefaultController\",\n    configurable: !0\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.mjs\n");

/***/ })

};
;