"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/agentkeepalive";
exports.ids = ["vendor-chunks/agentkeepalive"];
exports.modules = {

/***/ "(rsc)/./node_modules/agentkeepalive/index.js":
/*!**********************************************!*\
  !*** ./node_modules/agentkeepalive/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst HttpAgent = __webpack_require__(/*! ./lib/agent */ \"(rsc)/./node_modules/agentkeepalive/lib/agent.js\");\nmodule.exports = HttpAgent;\nmodule.exports.HttpAgent = HttpAgent;\nmodule.exports.HttpsAgent = __webpack_require__(/*! ./lib/https_agent */ \"(rsc)/./node_modules/agentkeepalive/lib/https_agent.js\");\nmodule.exports.constants = __webpack_require__(/*! ./lib/constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYWdlbnRrZWVwYWxpdmUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxNQUFNQSxZQUFZQyxtQkFBT0EsQ0FBQztBQUMxQkMsT0FBT0MsT0FBTyxHQUFHSDtBQUNqQkUsd0JBQXdCLEdBQUdGO0FBQzNCRSxrSUFBb0M7QUFDcENBLDZIQUFtQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhcmFuLXJvYXN0LWJvdC8uL25vZGVfbW9kdWxlcy9hZ2VudGtlZXBhbGl2ZS9pbmRleC5qcz8wYzI4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgSHR0cEFnZW50ID0gcmVxdWlyZSgnLi9saWIvYWdlbnQnKTtcbm1vZHVsZS5leHBvcnRzID0gSHR0cEFnZW50O1xubW9kdWxlLmV4cG9ydHMuSHR0cEFnZW50ID0gSHR0cEFnZW50O1xubW9kdWxlLmV4cG9ydHMuSHR0cHNBZ2VudCA9IHJlcXVpcmUoJy4vbGliL2h0dHBzX2FnZW50Jyk7XG5tb2R1bGUuZXhwb3J0cy5jb25zdGFudHMgPSByZXF1aXJlKCcuL2xpYi9jb25zdGFudHMnKTtcbiJdLCJuYW1lcyI6WyJIdHRwQWdlbnQiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsIkh0dHBzQWdlbnQiLCJjb25zdGFudHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/agent.js":
/*!**************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/agent.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst OriginalAgent = (__webpack_require__(/*! http */ \"http\").Agent);\nconst ms = __webpack_require__(/*! humanize-ms */ \"(rsc)/./node_modules/humanize-ms/index.js\");\nconst debug = (__webpack_require__(/*! util */ \"util\").debuglog)(\"agentkeepalive\");\nconst { INIT_SOCKET, CURRENT_ID, CREATE_ID, SOCKET_CREATED_TIME, SOCKET_NAME, SOCKET_REQUEST_COUNT, SOCKET_REQUEST_FINISHED_COUNT } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\n// OriginalAgent come from\n// - https://github.com/nodejs/node/blob/v8.12.0/lib/_http_agent.js\n// - https://github.com/nodejs/node/blob/v10.12.0/lib/_http_agent.js\n// node <= 10\nlet defaultTimeoutListenerCount = 1;\nconst majorVersion = parseInt(process.version.split(\".\", 1)[0].substring(1));\nif (majorVersion >= 11 && majorVersion <= 12) {\n    defaultTimeoutListenerCount = 2;\n} else if (majorVersion >= 13) {\n    defaultTimeoutListenerCount = 3;\n}\nfunction deprecate(message) {\n    console.log(\"[agentkeepalive:deprecated] %s\", message);\n}\nclass Agent extends OriginalAgent {\n    constructor(options){\n        options = options || {};\n        options.keepAlive = options.keepAlive !== false;\n        // default is keep-alive and 4s free socket timeout\n        // see https://medium.com/ssense-tech/reduce-networking-errors-in-nodejs-23b4eb9f2d83\n        if (options.freeSocketTimeout === undefined) {\n            options.freeSocketTimeout = 4000;\n        }\n        // Legacy API: keepAliveTimeout should be rename to `freeSocketTimeout`\n        if (options.keepAliveTimeout) {\n            deprecate(\"options.keepAliveTimeout is deprecated, please use options.freeSocketTimeout instead\");\n            options.freeSocketTimeout = options.keepAliveTimeout;\n            delete options.keepAliveTimeout;\n        }\n        // Legacy API: freeSocketKeepAliveTimeout should be rename to `freeSocketTimeout`\n        if (options.freeSocketKeepAliveTimeout) {\n            deprecate(\"options.freeSocketKeepAliveTimeout is deprecated, please use options.freeSocketTimeout instead\");\n            options.freeSocketTimeout = options.freeSocketKeepAliveTimeout;\n            delete options.freeSocketKeepAliveTimeout;\n        }\n        // Sets the socket to timeout after timeout milliseconds of inactivity on the socket.\n        // By default is double free socket timeout.\n        if (options.timeout === undefined) {\n            // make sure socket default inactivity timeout >= 8s\n            options.timeout = Math.max(options.freeSocketTimeout * 2, 8000);\n        }\n        // support humanize format\n        options.timeout = ms(options.timeout);\n        options.freeSocketTimeout = ms(options.freeSocketTimeout);\n        options.socketActiveTTL = options.socketActiveTTL ? ms(options.socketActiveTTL) : 0;\n        super(options);\n        this[CURRENT_ID] = 0;\n        // create socket success counter\n        this.createSocketCount = 0;\n        this.createSocketCountLastCheck = 0;\n        this.createSocketErrorCount = 0;\n        this.createSocketErrorCountLastCheck = 0;\n        this.closeSocketCount = 0;\n        this.closeSocketCountLastCheck = 0;\n        // socket error event count\n        this.errorSocketCount = 0;\n        this.errorSocketCountLastCheck = 0;\n        // request finished counter\n        this.requestCount = 0;\n        this.requestCountLastCheck = 0;\n        // including free socket timeout counter\n        this.timeoutSocketCount = 0;\n        this.timeoutSocketCountLastCheck = 0;\n        this.on(\"free\", (socket)=>{\n            // https://github.com/nodejs/node/pull/32000\n            // Node.js native agent will check socket timeout eqs agent.options.timeout.\n            // Use the ttl or freeSocketTimeout to overwrite.\n            const timeout = this.calcSocketTimeout(socket);\n            if (timeout > 0 && socket.timeout !== timeout) {\n                socket.setTimeout(timeout);\n            }\n        });\n    }\n    get freeSocketKeepAliveTimeout() {\n        deprecate(\"agent.freeSocketKeepAliveTimeout is deprecated, please use agent.options.freeSocketTimeout instead\");\n        return this.options.freeSocketTimeout;\n    }\n    get timeout() {\n        deprecate(\"agent.timeout is deprecated, please use agent.options.timeout instead\");\n        return this.options.timeout;\n    }\n    get socketActiveTTL() {\n        deprecate(\"agent.socketActiveTTL is deprecated, please use agent.options.socketActiveTTL instead\");\n        return this.options.socketActiveTTL;\n    }\n    calcSocketTimeout(socket) {\n        /**\n     * return <= 0: should free socket\n     * return > 0: should update socket timeout\n     * return undefined: not find custom timeout\n     */ let freeSocketTimeout = this.options.freeSocketTimeout;\n        const socketActiveTTL = this.options.socketActiveTTL;\n        if (socketActiveTTL) {\n            // check socketActiveTTL\n            const aliveTime = Date.now() - socket[SOCKET_CREATED_TIME];\n            const diff = socketActiveTTL - aliveTime;\n            if (diff <= 0) {\n                return diff;\n            }\n            if (freeSocketTimeout && diff < freeSocketTimeout) {\n                freeSocketTimeout = diff;\n            }\n        }\n        // set freeSocketTimeout\n        if (freeSocketTimeout) {\n            // set free keepalive timer\n            // try to use socket custom freeSocketTimeout first, support headers['keep-alive']\n            // https://github.com/node-modules/urllib/blob/b76053020923f4d99a1c93cf2e16e0c5ba10bacf/lib/urllib.js#L498\n            const customFreeSocketTimeout = socket.freeSocketTimeout || socket.freeSocketKeepAliveTimeout;\n            return customFreeSocketTimeout || freeSocketTimeout;\n        }\n    }\n    keepSocketAlive(socket) {\n        const result = super.keepSocketAlive(socket);\n        // should not keepAlive, do nothing\n        if (!result) return result;\n        const customTimeout = this.calcSocketTimeout(socket);\n        if (typeof customTimeout === \"undefined\") {\n            return true;\n        }\n        if (customTimeout <= 0) {\n            debug(\"%s(requests: %s, finished: %s) free but need to destroy by TTL, request count %s, diff is %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], customTimeout);\n            return false;\n        }\n        if (socket.timeout !== customTimeout) {\n            socket.setTimeout(customTimeout);\n        }\n        return true;\n    }\n    // only call on addRequest\n    reuseSocket(...args) {\n        // reuseSocket(socket, req)\n        super.reuseSocket(...args);\n        const socket = args[0];\n        const req = args[1];\n        req.reusedSocket = true;\n        const agentTimeout = this.options.timeout;\n        if (getSocketTimeout(socket) !== agentTimeout) {\n            // reset timeout before use\n            socket.setTimeout(agentTimeout);\n            debug(\"%s reset timeout to %sms\", socket[SOCKET_NAME], agentTimeout);\n        }\n        socket[SOCKET_REQUEST_COUNT]++;\n        debug(\"%s(requests: %s, finished: %s) reuse on addRequest, timeout %sms\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], getSocketTimeout(socket));\n    }\n    [CREATE_ID]() {\n        const id = this[CURRENT_ID]++;\n        if (this[CURRENT_ID] === Number.MAX_SAFE_INTEGER) this[CURRENT_ID] = 0;\n        return id;\n    }\n    [INIT_SOCKET](socket, options) {\n        // bugfix here.\n        // https on node 8, 10 won't set agent.options.timeout by default\n        // TODO: need to fix on node itself\n        if (options.timeout) {\n            const timeout = getSocketTimeout(socket);\n            if (!timeout) {\n                socket.setTimeout(options.timeout);\n            }\n        }\n        if (this.options.keepAlive) {\n            // Disable Nagle's algorithm: http://blog.caustik.com/2012/04/08/scaling-node-js-to-100k-concurrent-connections/\n            // https://fengmk2.com/benchmark/nagle-algorithm-delayed-ack-mock.html\n            socket.setNoDelay(true);\n        }\n        this.createSocketCount++;\n        if (this.options.socketActiveTTL) {\n            socket[SOCKET_CREATED_TIME] = Date.now();\n        }\n        // don't show the hole '-----BEGIN CERTIFICATE----' key string\n        socket[SOCKET_NAME] = `sock[${this[CREATE_ID]()}#${options._agentKey}]`.split(\"-----BEGIN\", 1)[0];\n        socket[SOCKET_REQUEST_COUNT] = 1;\n        socket[SOCKET_REQUEST_FINISHED_COUNT] = 0;\n        installListeners(this, socket, options);\n    }\n    createConnection(options, oncreate) {\n        let called = false;\n        const onNewCreate = (err, socket)=>{\n            if (called) return;\n            called = true;\n            if (err) {\n                this.createSocketErrorCount++;\n                return oncreate(err);\n            }\n            this[INIT_SOCKET](socket, options);\n            oncreate(err, socket);\n        };\n        const newSocket = super.createConnection(options, onNewCreate);\n        if (newSocket) onNewCreate(null, newSocket);\n        return newSocket;\n    }\n    get statusChanged() {\n        const changed = this.createSocketCount !== this.createSocketCountLastCheck || this.createSocketErrorCount !== this.createSocketErrorCountLastCheck || this.closeSocketCount !== this.closeSocketCountLastCheck || this.errorSocketCount !== this.errorSocketCountLastCheck || this.timeoutSocketCount !== this.timeoutSocketCountLastCheck || this.requestCount !== this.requestCountLastCheck;\n        if (changed) {\n            this.createSocketCountLastCheck = this.createSocketCount;\n            this.createSocketErrorCountLastCheck = this.createSocketErrorCount;\n            this.closeSocketCountLastCheck = this.closeSocketCount;\n            this.errorSocketCountLastCheck = this.errorSocketCount;\n            this.timeoutSocketCountLastCheck = this.timeoutSocketCount;\n            this.requestCountLastCheck = this.requestCount;\n        }\n        return changed;\n    }\n    getCurrentStatus() {\n        return {\n            createSocketCount: this.createSocketCount,\n            createSocketErrorCount: this.createSocketErrorCount,\n            closeSocketCount: this.closeSocketCount,\n            errorSocketCount: this.errorSocketCount,\n            timeoutSocketCount: this.timeoutSocketCount,\n            requestCount: this.requestCount,\n            freeSockets: inspect(this.freeSockets),\n            sockets: inspect(this.sockets),\n            requests: inspect(this.requests)\n        };\n    }\n}\n// node 8 don't has timeout attribute on socket\n// https://github.com/nodejs/node/pull/21204/files#diff-e6ef024c3775d787c38487a6309e491dR408\nfunction getSocketTimeout(socket) {\n    return socket.timeout || socket._idleTimeout;\n}\nfunction installListeners(agent, socket, options) {\n    debug(\"%s create, timeout %sms\", socket[SOCKET_NAME], getSocketTimeout(socket));\n    // listener socket events: close, timeout, error, free\n    function onFree() {\n        // create and socket.emit('free') logic\n        // https://github.com/nodejs/node/blob/master/lib/_http_agent.js#L311\n        // no req on the socket, it should be the new socket\n        if (!socket._httpMessage && socket[SOCKET_REQUEST_COUNT] === 1) return;\n        socket[SOCKET_REQUEST_FINISHED_COUNT]++;\n        agent.requestCount++;\n        debug(\"%s(requests: %s, finished: %s) free\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n        // should reuse on pedding requests?\n        const name = agent.getName(options);\n        if (socket.writable && agent.requests[name] && agent.requests[name].length) {\n            // will be reuse on agent free listener\n            socket[SOCKET_REQUEST_COUNT]++;\n            debug(\"%s(requests: %s, finished: %s) will be reuse on agent free event\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n        }\n    }\n    socket.on(\"free\", onFree);\n    function onClose(isError) {\n        debug(\"%s(requests: %s, finished: %s) close, isError: %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], isError);\n        agent.closeSocketCount++;\n    }\n    socket.on(\"close\", onClose);\n    // start socket timeout handler\n    function onTimeout() {\n        // onTimeout and emitRequestTimeout(_http_client.js)\n        // https://github.com/nodejs/node/blob/v12.x/lib/_http_client.js#L711\n        const listenerCount = socket.listeners(\"timeout\").length;\n        // node <= 10, default listenerCount is 1, onTimeout\n        // 11 < node <= 12, default listenerCount is 2, onTimeout and emitRequestTimeout\n        // node >= 13, default listenerCount is 3, onTimeout,\n        //   onTimeout(https://github.com/nodejs/node/pull/32000/files#diff-5f7fb0850412c6be189faeddea6c5359R333)\n        //   and emitRequestTimeout\n        const timeout = getSocketTimeout(socket);\n        const req = socket._httpMessage;\n        const reqTimeoutListenerCount = req && req.listeners(\"timeout\").length || 0;\n        debug(\"%s(requests: %s, finished: %s) timeout after %sms, listeners %s, defaultTimeoutListenerCount %s, hasHttpRequest %s, HttpRequest timeoutListenerCount %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], timeout, listenerCount, defaultTimeoutListenerCount, !!req, reqTimeoutListenerCount);\n        if (debug.enabled) {\n            debug(\"timeout listeners: %s\", socket.listeners(\"timeout\").map((f)=>f.name).join(\", \"));\n        }\n        agent.timeoutSocketCount++;\n        const name = agent.getName(options);\n        if (agent.freeSockets[name] && agent.freeSockets[name].indexOf(socket) !== -1) {\n            // free socket timeout, destroy quietly\n            socket.destroy();\n            // Remove it from freeSockets list immediately to prevent new requests\n            // from being sent through this socket.\n            agent.removeSocket(socket, options);\n            debug(\"%s is free, destroy quietly\", socket[SOCKET_NAME]);\n        } else {\n            // if there is no any request socket timeout handler,\n            // agent need to handle socket timeout itself.\n            //\n            // custom request socket timeout handle logic must follow these rules:\n            //  1. Destroy socket first\n            //  2. Must emit socket 'agentRemove' event tell agent remove socket\n            //     from freeSockets list immediately.\n            //     Otherise you may be get 'socket hang up' error when reuse\n            //     free socket and timeout happen in the same time.\n            if (reqTimeoutListenerCount === 0) {\n                const error = new Error(\"Socket timeout\");\n                error.code = \"ERR_SOCKET_TIMEOUT\";\n                error.timeout = timeout;\n                // must manually call socket.end() or socket.destroy() to end the connection.\n                // https://nodejs.org/dist/latest-v10.x/docs/api/net.html#net_socket_settimeout_timeout_callback\n                socket.destroy(error);\n                agent.removeSocket(socket, options);\n                debug(\"%s destroy with timeout error\", socket[SOCKET_NAME]);\n            }\n        }\n    }\n    socket.on(\"timeout\", onTimeout);\n    function onError(err) {\n        const listenerCount = socket.listeners(\"error\").length;\n        debug(\"%s(requests: %s, finished: %s) error: %s, listenerCount: %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], err, listenerCount);\n        agent.errorSocketCount++;\n        if (listenerCount === 1) {\n            // if socket don't contain error event handler, don't catch it, emit it again\n            debug(\"%s emit uncaught error event\", socket[SOCKET_NAME]);\n            socket.removeListener(\"error\", onError);\n            socket.emit(\"error\", err);\n        }\n    }\n    socket.on(\"error\", onError);\n    function onRemove() {\n        debug(\"%s(requests: %s, finished: %s) agentRemove\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n        // We need this function for cases like HTTP 'upgrade'\n        // (defined by WebSockets) where we need to remove a socket from the\n        // pool because it'll be locked up indefinitely\n        socket.removeListener(\"close\", onClose);\n        socket.removeListener(\"error\", onError);\n        socket.removeListener(\"free\", onFree);\n        socket.removeListener(\"timeout\", onTimeout);\n        socket.removeListener(\"agentRemove\", onRemove);\n    }\n    socket.on(\"agentRemove\", onRemove);\n}\nmodule.exports = Agent;\nfunction inspect(obj) {\n    const res = {};\n    for(const key in obj){\n        res[key] = obj[key].length;\n    }\n    return res;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/agent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/constants.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    // agent\n    CURRENT_ID: Symbol(\"agentkeepalive#currentId\"),\n    CREATE_ID: Symbol(\"agentkeepalive#createId\"),\n    INIT_SOCKET: Symbol(\"agentkeepalive#initSocket\"),\n    CREATE_HTTPS_CONNECTION: Symbol(\"agentkeepalive#createHttpsConnection\"),\n    // socket\n    SOCKET_CREATED_TIME: Symbol(\"agentkeepalive#socketCreatedTime\"),\n    SOCKET_NAME: Symbol(\"agentkeepalive#socketName\"),\n    SOCKET_REQUEST_COUNT: Symbol(\"agentkeepalive#socketRequestCount\"),\n    SOCKET_REQUEST_FINISHED_COUNT: Symbol(\"agentkeepalive#socketRequestFinishedCount\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYWdlbnRrZWVwYWxpdmUvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSxPQUFPQyxPQUFPLEdBQUc7SUFDZixRQUFRO0lBQ1JDLFlBQVlDLE9BQU87SUFDbkJDLFdBQVdELE9BQU87SUFDbEJFLGFBQWFGLE9BQU87SUFDcEJHLHlCQUF5QkgsT0FBTztJQUNoQyxTQUFTO0lBQ1RJLHFCQUFxQkosT0FBTztJQUM1QkssYUFBYUwsT0FBTztJQUNwQk0sc0JBQXNCTixPQUFPO0lBQzdCTywrQkFBK0JQLE9BQU87QUFDeEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYXJhbi1yb2FzdC1ib3QvLi9ub2RlX21vZHVsZXMvYWdlbnRrZWVwYWxpdmUvbGliL2NvbnN0YW50cy5qcz8zOTFmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIC8vIGFnZW50XG4gIENVUlJFTlRfSUQ6IFN5bWJvbCgnYWdlbnRrZWVwYWxpdmUjY3VycmVudElkJyksXG4gIENSRUFURV9JRDogU3ltYm9sKCdhZ2VudGtlZXBhbGl2ZSNjcmVhdGVJZCcpLFxuICBJTklUX1NPQ0tFVDogU3ltYm9sKCdhZ2VudGtlZXBhbGl2ZSNpbml0U29ja2V0JyksXG4gIENSRUFURV9IVFRQU19DT05ORUNUSU9OOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI2NyZWF0ZUh0dHBzQ29ubmVjdGlvbicpLFxuICAvLyBzb2NrZXRcbiAgU09DS0VUX0NSRUFURURfVElNRTogU3ltYm9sKCdhZ2VudGtlZXBhbGl2ZSNzb2NrZXRDcmVhdGVkVGltZScpLFxuICBTT0NLRVRfTkFNRTogU3ltYm9sKCdhZ2VudGtlZXBhbGl2ZSNzb2NrZXROYW1lJyksXG4gIFNPQ0tFVF9SRVFVRVNUX0NPVU5UOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI3NvY2tldFJlcXVlc3RDb3VudCcpLFxuICBTT0NLRVRfUkVRVUVTVF9GSU5JU0hFRF9DT1VOVDogU3ltYm9sKCdhZ2VudGtlZXBhbGl2ZSNzb2NrZXRSZXF1ZXN0RmluaXNoZWRDb3VudCcpLFxufTtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiQ1VSUkVOVF9JRCIsIlN5bWJvbCIsIkNSRUFURV9JRCIsIklOSVRfU09DS0VUIiwiQ1JFQVRFX0hUVFBTX0NPTk5FQ1RJT04iLCJTT0NLRVRfQ1JFQVRFRF9USU1FIiwiU09DS0VUX05BTUUiLCJTT0NLRVRfUkVRVUVTVF9DT1VOVCIsIlNPQ0tFVF9SRVFVRVNUX0ZJTklTSEVEX0NPVU5UIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/https_agent.js":
/*!********************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/https_agent.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst OriginalHttpsAgent = (__webpack_require__(/*! https */ \"https\").Agent);\nconst HttpAgent = __webpack_require__(/*! ./agent */ \"(rsc)/./node_modules/agentkeepalive/lib/agent.js\");\nconst { INIT_SOCKET, CREATE_HTTPS_CONNECTION } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\nclass HttpsAgent extends HttpAgent {\n    constructor(options){\n        super(options);\n        this.defaultPort = 443;\n        this.protocol = \"https:\";\n        this.maxCachedSessions = this.options.maxCachedSessions;\n        /* istanbul ignore next */ if (this.maxCachedSessions === undefined) {\n            this.maxCachedSessions = 100;\n        }\n        this._sessionCache = {\n            map: {},\n            list: []\n        };\n    }\n    createConnection(options, oncreate) {\n        const socket = this[CREATE_HTTPS_CONNECTION](options, oncreate);\n        this[INIT_SOCKET](socket, options);\n        return socket;\n    }\n}\n// https://github.com/nodejs/node/blob/master/lib/https.js#L89\nHttpsAgent.prototype[CREATE_HTTPS_CONNECTION] = OriginalHttpsAgent.prototype.createConnection;\n[\n    \"getName\",\n    \"_getSession\",\n    \"_cacheSession\",\n    // https://github.com/nodejs/node/pull/4982\n    \"_evictSession\"\n].forEach(function(method) {\n    /* istanbul ignore next */ if (typeof OriginalHttpsAgent.prototype[method] === \"function\") {\n        HttpsAgent.prototype[method] = OriginalHttpsAgent.prototype[method];\n    }\n});\nmodule.exports = HttpsAgent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/https_agent.js\n");

/***/ })

};
;