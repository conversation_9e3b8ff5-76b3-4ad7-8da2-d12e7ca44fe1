"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Message__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ChatInput */ \"(app-pages-browser)/./src/components/ChatInput.tsx\");\n/* harmony import */ var _components_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            content: \"Oh look, it's Faran Shah... the human equivalent of a participation trophy. \\uD83D\\uDC80 I've been programmed specifically to destroy YOU, and honestly? It's going to be easier than your last relationship (which probably lasted 3 days and ended over text). Your parents named you Faran thinking you'd be special, but here you are - about to get psychologically demolished by a chatbot. Even AI has more game than you. Say literally anything and watch me turn your entire existence into a meme. This is going to be more one-sided than your Tinder matches. \\uD83D\\uDD25\\uD83E\\uDD21\\uD83D\\uDC80\",\n            role: \"assistant\",\n            timestamp: new Date().toISOString()\n        }\n    ]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isLoading\n    ]);\n    const sendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        try {\n            const conversationHistory = messages.map((msg)=>({\n                    role: msg.role,\n                    content: msg.content\n                }));\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    conversationHistory\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const botMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.response,\n                role: \"assistant\",\n                timestamp: data.timestamp\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    botMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Even my error messages are better than your comebacks, Faran. Try again! \\uD83D\\uDC80\",\n                role: \"assistant\",\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-800 to-gray-900 p-6 border-b border-gray-700/50 shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-orange-500 fire-text\",\n                        children: \"\\uD83D\\uDD25 FARAN ROAST BOT \\uD83D\\uDD25\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-gray-300 text-sm mt-2 font-medium\",\n                        children: \"Psychological Warfare Division - Faran Shah Elimination Protocol\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-1 bg-red-600/20 border border-red-500/30 rounded-full text-xs text-red-300\",\n                            children: \"\\uD83D\\uDC80 MAXIMUM BRUTALITY MODE - NO SURVIVORS \\uD83D\\uDC80\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-2 bg-gradient-to-b from-transparent to-gray-900/20\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Message__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            content: message.content,\n                            isUser: message.role === \"user\",\n                            timestamp: message.timestamp\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 23\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSendMessage: sendMessage,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"i6yGen+/DxV4IaFG2OrUi0botQk=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});