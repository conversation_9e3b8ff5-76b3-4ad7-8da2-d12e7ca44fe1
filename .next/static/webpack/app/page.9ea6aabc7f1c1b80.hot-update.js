"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Message__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ChatInput */ \"(app-pages-browser)/./src/components/ChatInput.tsx\");\n/* harmony import */ var _components_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingWelcome, setIsGeneratingWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const generateWelcomeMessage = async ()=>{\n        try {\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: \"Generate a brutal welcome message for when Faran Shah first opens this chat. Make it personal, dark, and absolutely savage. This is the first thing he'll see.\",\n                    conversationHistory: []\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate welcome message\");\n            }\n            const data = await response.json();\n            const welcomeMessage = {\n                id: \"1\",\n                content: data.response,\n                role: \"assistant\",\n                timestamp: data.timestamp\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n        } catch (error) {\n            console.error(\"Error generating welcome message:\", error);\n            // Fallback welcome message\n            const fallbackMessage = {\n                id: \"1\",\n                content: \"Oh look, it's Faran Shah... the human equivalent of a participation trophy. \\uD83D\\uDC80 Ready to get absolutely destroyed? Because I've been waiting specifically for YOU. Say something and watch me turn your entire existence into a meme. \\uD83D\\uDD25\\uD83E\\uDD21\\uD83D\\uDC80\",\n                role: \"assistant\",\n                timestamp: new Date().toISOString()\n            };\n            setMessages([\n                fallbackMessage\n            ]);\n        } finally{\n            setIsGeneratingWelcome(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        generateWelcomeMessage();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isLoading\n    ]);\n    const sendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        try {\n            const conversationHistory = messages.map((msg)=>({\n                    role: msg.role,\n                    content: msg.content\n                }));\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    conversationHistory\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const botMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.response,\n                role: \"assistant\",\n                timestamp: data.timestamp\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    botMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Even my error messages are better than your comebacks, Faran. Try again! \\uD83D\\uDC80\",\n                role: \"assistant\",\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-800 to-gray-900 p-6 border-b border-gray-700/50 shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-orange-500 fire-text\",\n                        children: \"\\uD83D\\uDD25 FARAN ROAST BOT \\uD83D\\uDD25\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-gray-300 text-sm mt-2 font-medium\",\n                        children: \"Psychological Warfare Division - Faran Shah Elimination Protocol\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-2 bg-gradient-to-b from-transparent to-gray-900/20\",\n                children: [\n                    isGeneratingWelcome ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Message__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    content: message.content,\n                                    isUser: message.role === \"user\",\n                                    timestamp: message.timestamp\n                                }, message.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 27\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSendMessage: sendMessage,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Faran-roast/src/app/page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"duHLjBkXxFpUM+qdkPmh1KJU1WA=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});