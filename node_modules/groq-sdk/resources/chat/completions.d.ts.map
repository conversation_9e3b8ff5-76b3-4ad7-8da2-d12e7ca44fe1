{"version": 3, "file": "completions.d.ts", "sourceRoot": "", "sources": ["../../src/resources/chat/completions.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AACnC,OAAO,KAAK,kBAAkB,MAAM,eAAe,CAAC;AACpD,OAAO,KAAK,cAAc,MAAM,gBAAgB,CAAC;AACjD,OAAO,KAAK,MAAM,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,qBAAa,WAAY,SAAQ,WAAW;IAC1C;;OAEG;IACH,MAAM,CACJ,IAAI,EAAE,sCAAsC,EAC5C,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAClC,MAAM,CACJ,IAAI,EAAE,mCAAmC,EACzC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAC/C,MAAM,CACJ,IAAI,EAAE,8BAA8B,EACpC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,cAAc,CAAC;CAWjE;AAED;;;GAGG;AACH,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;OAGG;IACH,OAAO,EAAE,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAEtC;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,MAAM,EAAE,iBAAiB,CAAC;IAE1B;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B;;OAEG;IACH,KAAK,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC;CACxC;AAED,yBAAiB,cAAc,CAAC;IAC9B,UAAiB,MAAM;QACrB;;;;;WAKG;QACH,aAAa,EAAE,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;QAEjC;;WAEG;QACH,OAAO,EAAE,kBAAkB,CAAC,qBAAqB,CAAC;KACnD;IAED,UAAiB,MAAM,CAAC;QACtB;;WAEG;QACH,UAAiB,QAAQ;YACvB;;eAEG;YACH,OAAO,EAAE,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC;SACtE;KACF;CACF;AAED,MAAM,WAAW,mCAAmC;IAClD;;OAEG;IACH,IAAI,EAAE,WAAW,CAAC;IAElB;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAExB;;;OAGG;IACH,aAAa,CAAC,EAAE,mCAAmC,CAAC,YAAY,CAAC;IAEjE;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,6BAA6B,CAAC,CAAC;CACnD;AAED,yBAAiB,mCAAmC,CAAC;IACnD;;;OAGG;IACH,UAAiB,YAAY;QAC3B;;;;;WAKG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;OAGG;IACH,OAAO,EAAE,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAE3C;;;OAGG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,MAAM,EAAE,uBAAuB,CAAC;IAEhC;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B,MAAM,CAAC,EAAE,mBAAmB,CAAC,KAAK,CAAC;CACpC;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,MAAM;QACrB;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;QAEpB;;;;;WAKG;QACH,aAAa,EAAE,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,eAAe,GAAG,IAAI,CAAC;QAEzE;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;KACnC;IAED,UAAiB,MAAM,CAAC;QACtB;;WAEG;QACH,UAAiB,KAAK;YACpB;;eAEG;YACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAExB;;;eAGG;YACH,aAAa,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC;YAEnC;;eAEG;YACH,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,GAAG,WAAW,GAAG,MAAM,CAAC;YAEhD,UAAU,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;SACpC;QAED,UAAiB,KAAK,CAAC;YACrB;;;eAGG;YACH,UAAiB,YAAY;gBAC3B;;;;;mBAKG;gBACH,SAAS,CAAC,EAAE,MAAM,CAAC;gBAEnB;;mBAEG;gBACH,IAAI,CAAC,EAAE,MAAM,CAAC;aACf;YAED,UAAiB,QAAQ;gBACvB,KAAK,EAAE,MAAM,CAAC;gBAEd;;mBAEG;gBACH,EAAE,CAAC,EAAE,MAAM,CAAC;gBAEZ,QAAQ,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC;gBAE7B;;mBAEG;gBACH,IAAI,CAAC,EAAE,UAAU,CAAC;aACnB;YAED,UAAiB,QAAQ,CAAC;gBACxB,UAAiB,QAAQ;oBACvB;;;;;uBAKG;oBACH,SAAS,CAAC,EAAE,MAAM,CAAC;oBAEnB;;uBAEG;oBACH,IAAI,CAAC,EAAE,MAAM,CAAC;iBACf;aACF;SACF;QAED;;WAEG;QACH,UAAiB,QAAQ;YACvB;;eAEG;YACH,OAAO,EAAE,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC;SACtE;KACF;IAED,UAAiB,KAAK;QACpB;;;WAGG;QACH,EAAE,CAAC,EAAE,MAAM,CAAC;QAEZ;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;WAEG;QACH,KAAK,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC;KACxC;CACF;AAED,MAAM,MAAM,yBAAyB,GAAG,6BAA6B,GAAG,8BAA8B,CAAC;AAEvG,MAAM,WAAW,8BAA8B;IAC7C,SAAS,EAAE,8BAA8B,CAAC,QAAQ,CAAC;IAEnD;;OAEG;IACH,IAAI,EAAE,WAAW,CAAC;CACnB;AAED,yBAAiB,8BAA8B,CAAC;IAC9C,UAAiB,QAAQ;QACvB;;WAEG;QACH,GAAG,EAAE,MAAM,CAAC;QAEZ;;WAEG;QACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;KAClC;CACF;AAED,MAAM,WAAW,6BAA6B;IAC5C;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;;GAGG;AACH,MAAM,WAAW,gCAAgC;IAC/C;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;GAEG;AACH,MAAM,WAAW,kCAAkC;IACjD;;OAEG;IACH,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,EAAE,UAAU,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvB;;OAEG;IACH,IAAI,EAAE,WAAW,CAAC;IAElB;;;OAGG;IACH,aAAa,CAAC,EAAE,qBAAqB,CAAC,YAAY,CAAC;IAEnD;;OAEG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,6BAA6B,CAAC,CAAC;CACnD;AAED,yBAAiB,qBAAqB,CAAC;IACrC;;;OAGG;IACH,UAAiB,YAAY;QAC3B;;;;;WAKG;QACH,SAAS,EAAE,MAAM,CAAC;QAElB;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;KACd;CACF;AAED,MAAM,MAAM,0BAA0B,GAClC,gCAAgC,GAChC,8BAA8B,GAC9B,mCAAmC,GACnC,8BAA8B,GAC9B,kCAAkC,CAAC;AAEvC,MAAM,WAAW,6BAA6B;IAC5C;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,QAAQ,EAAE,6BAA6B,CAAC,QAAQ,CAAC;IAEjD;;OAEG;IACH,IAAI,EAAE,UAAU,CAAC;CAClB;AAED,yBAAiB,6BAA6B,CAAC;IAC7C;;OAEG;IACH,UAAiB,QAAQ;QACvB;;;;;WAKG;QACH,SAAS,EAAE,MAAM,CAAC;QAElB;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;KACd;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,6BAA6B;IAC5C,QAAQ,EAAE,6BAA6B,CAAC,QAAQ,CAAC;IAEjD;;OAEG;IACH,IAAI,EAAE,UAAU,CAAC;CAClB;AAED,yBAAiB,6BAA6B,CAAC;IAC7C,UAAiB,QAAQ;QACvB;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;KACd;CACF;AAED;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,QAAQ,GAAG,MAAM,GAAG,WAAW,GAAG,MAAM,GAAG,UAAU,CAAC;AAEvF,MAAM,WAAW,gCAAgC;IAC/C;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,IAAI,EAAE,QAAQ,CAAC;IAEf;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;;;;OAKG;IACH,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;IAE5B;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;;OAIG;IACH,YAAY,EAAE,KAAK,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;CAC5D;AAED,yBAAiB,0BAA0B,CAAC;IAC1C,UAAiB,UAAU;QACzB;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;QAEd;;;;;WAKG;QACH,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAE5B;;;;WAIG;QACH,OAAO,EAAE,MAAM,CAAC;KACjB;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC;IAEpC;;OAEG;IACH,IAAI,EAAE,UAAU,CAAC;CAClB;AAED;;;;;;;;;;GAUG;AACH,MAAM,MAAM,8BAA8B,GAAG,MAAM,GAAG,MAAM,GAAG,UAAU,GAAG,6BAA6B,CAAC;AAE1G,MAAM,WAAW,8BAA8B;IAC7C;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,8BAA8B;IAC7C;;OAEG;IACH,OAAO,EAAE,MAAM,GAAG,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAEnD;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,MAAM,0BAA0B,GAClC,sCAAsC,GACtC,mCAAmC,CAAC;AAExC,MAAM,WAAW,8BAA8B;IAC7C;;OAEG;IACH,QAAQ,EAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAE5C;;;OAGG;IACH,KAAK,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,aAAa,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,oBAAoB,CAAC;IAEnG;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElC;;;;;;;;;;;OAWG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,UAAU,GAAG,gCAAgC,GAAG,IAAI,CAAC;IAEvF;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,sBAAsB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;IAE1D;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;IAE3C;;;;OAIG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAE1B;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B;;;;OAIG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;OAEG;IACH,mBAAmB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAErC;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjC;;;;;;;;OAQG;IACH,eAAe,CAAC,EAAE,sBAAsB,CAAC,cAAc,GAAG,IAAI,CAAC;IAE/D;;;;;OAKG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErB;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAErC;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAExB;;;;;OAKG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;;;;;;;;;OAUG;IACH,WAAW,CAAC,EAAE,8BAA8B,GAAG,IAAI,CAAC;IAEpD;;;;OAIG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;IAEzC;;;;;OAKG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B;;;;;OAKG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEtB;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACtB;AAED,yBAAiB,sBAAsB,CAAC;IACtC;;OAEG;IACH,UAAiB,QAAQ;QACvB;;;WAGG;QACH,IAAI,EAAE,MAAM,CAAC;QAEb;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;;;;;;WAOG;QACH,UAAU,CAAC,EAAE,MAAM,CAAC,kBAAkB,CAAC;KACxC;IAED;;;;;;;;OAQG;IACH,UAAiB,cAAc;QAC7B;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,sCAAuC,SAAQ,8BAA8B;IAC5F;;;;;OAKG;IACH,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,mCAAoC,SAAQ,8BAA8B;IACzF;;;;;OAKG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,yBAAiB,WAAW,CAAC;IAC3B,MAAM,QAAQ,cAAc,GAAG,kBAAkB,CAAC,cAAc,CAAC;IACjE,MAAM,QAAQ,mCAAmC,GAAG,kBAAkB,CAAC,mCAAmC,CAAC;IAC3G,MAAM,QAAQ,mBAAmB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC;IAC3E,MAAM,QAAQ,yBAAyB,GAAG,kBAAkB,CAAC,yBAAyB,CAAC;IACvF,MAAM,QAAQ,8BAA8B,GAAG,kBAAkB,CAAC,8BAA8B,CAAC;IACjG,MAAM,QAAQ,6BAA6B,GAAG,kBAAkB,CAAC,6BAA6B,CAAC;IAC/F,MAAM,QAAQ,gCAAgC,GAAG,kBAAkB,CAAC,gCAAgC,CAAC;IACrG,MAAM,QAAQ,kCAAkC,GAAG,kBAAkB,CAAC,kCAAkC,CAAC;IACzG,MAAM,QAAQ,qBAAqB,GAAG,kBAAkB,CAAC,qBAAqB,CAAC;IAC/E,MAAM,QAAQ,0BAA0B,GAAG,kBAAkB,CAAC,0BAA0B,CAAC;IACzF,MAAM,QAAQ,6BAA6B,GAAG,kBAAkB,CAAC,6BAA6B,CAAC;IAC/F,MAAM,QAAQ,6BAA6B,GAAG,kBAAkB,CAAC,6BAA6B,CAAC;IAC/F,MAAM,QAAQ,kBAAkB,GAAG,kBAAkB,CAAC,kBAAkB,CAAC;IACzE,MAAM,QAAQ,gCAAgC,GAAG,kBAAkB,CAAC,gCAAgC,CAAC;IACrG,MAAM,QAAQ,0BAA0B,GAAG,kBAAkB,CAAC,0BAA0B,CAAC;IACzF,MAAM,QAAQ,kBAAkB,GAAG,kBAAkB,CAAC,kBAAkB,CAAC;IACzE,MAAM,QAAQ,8BAA8B,GAAG,kBAAkB,CAAC,8BAA8B,CAAC;IACjG,MAAM,QAAQ,8BAA8B,GAAG,kBAAkB,CAAC,8BAA8B,CAAC;IACjG,MAAM,QAAQ,8BAA8B,GAAG,kBAAkB,CAAC,8BAA8B,CAAC;IACjG,MAAM,QAAQ,sBAAsB,GAAG,kBAAkB,CAAC,sBAAsB,CAAC;CAClF"}