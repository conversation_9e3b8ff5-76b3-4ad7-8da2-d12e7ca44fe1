import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
import * as TranslationsAPI from "./translations.js";
export declare class Translations extends APIResource {
    /**
     * Translates audio into English.
     */
    create(body: TranslationCreateParams, options?: Core.RequestOptions): Core.APIPromise<Translation>;
}
export interface Translation {
    text: string;
}
export interface TranslationCreateParams {
    /**
     * The audio file object (not file name) translate, in one of these formats: flac,
     * mp3, mp4, mpeg, mpga, m4a, ogg, wav, or webm.
     */
    file: Core.Uploadable;
    /**
     * ID of the model to use. Only `whisper-large-v3` is currently available.
     */
    model: (string & {}) | 'whisper-large-v3';
    /**
     * An optional text to guide the model's style or continue a previous audio
     * segment. The [prompt](/docs/guides/speech-to-text/prompting) should be in
     * English.
     */
    prompt?: string;
    /**
     * The format of the transcript output, in one of these options: `json`, `text`, or
     * `verbose_json`.
     */
    response_format?: 'json' | 'text' | 'verbose_json';
    /**
     * The sampling temperature, between 0 and 1. Higher values like 0.8 will make the
     * output more random, while lower values like 0.2 will make it more focused and
     * deterministic. If set to 0, the model will use
     * [log probability](https://en.wikipedia.org/wiki/Log_probability) to
     * automatically increase the temperature until certain thresholds are hit.
     */
    temperature?: number;
}
export declare namespace Translations {
    export import Translation = TranslationsAPI.Translation;
    export import TranslationCreateParams = TranslationsAPI.TranslationCreateParams;
}
//# sourceMappingURL=translations.d.ts.map