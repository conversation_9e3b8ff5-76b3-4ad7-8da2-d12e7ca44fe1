{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["src/index.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,MAAM,MAAM,SAAS,CAAC;AAClC,OAAO,KAAK,OAAO,MAAM,WAAW,CAAC;AACrC,OAAO,EAAE,KAAK,KAAK,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,KAAK,IAAI,MAAM,QAAQ,CAAC;AAC/B,OAAO,KAAK,GAAG,MAAM,mBAAmB,CAAC;AAEzC,MAAM,WAAW,aAAa;IAC5B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE5B;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAEpC;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;;;;OAKG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC;IAElB;;;;;OAKG;IACH,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;IAE/B;;;;;OAKG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;;;;OAKG;IACH,cAAc,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC;IAE9B;;;;;OAKG;IACH,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC;IAEjC;;;OAGG;IACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;CACnC;AAED;;GAEG;AACH,qBAAa,IAAK,SAAQ,IAAI,CAAC,SAAS;IACtC,MAAM,EAAE,MAAM,CAAC;IAEf,OAAO,CAAC,QAAQ,CAAgB;IAEhC;;;;;;;;;;;;OAYG;gBACS,EACV,OAAuC,EACvC,MAAqC,EACrC,GAAG,IAAI,EACR,GAAE,aAAkB;IAgCrB,WAAW,EAAE,GAAG,CAAC,WAAW,CAA6B;IACzD,IAAI,EAAE,GAAG,CAAC,IAAI,CAAsB;IACpC,UAAU,EAAE,GAAG,CAAC,UAAU,CAA4B;IACtD,KAAK,EAAE,GAAG,CAAC,KAAK,CAAuB;IACvC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAwB;cAEvB,YAAY,IAAI,IAAI,CAAC,YAAY,GAAG,SAAS;cAI7C,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO;cAO5D,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO;IAI5E,MAAM,CAAC,IAAI,cAAQ;IACnB,MAAM,CAAC,eAAe,SAAS;IAE/B,MAAM,CAAC,SAAS,0BAAoB;IACpC,MAAM,CAAC,QAAQ,yBAAmB;IAClC,MAAM,CAAC,kBAAkB,mCAA6B;IACtD,MAAM,CAAC,yBAAyB,0CAAoC;IACpE,MAAM,CAAC,iBAAiB,kCAA4B;IACpD,MAAM,CAAC,aAAa,8BAAwB;IAC5C,MAAM,CAAC,aAAa,8BAAwB;IAC5C,MAAM,CAAC,cAAc,+BAAyB;IAC9C,MAAM,CAAC,eAAe,gCAA0B;IAChD,MAAM,CAAC,mBAAmB,oCAA8B;IACxD,MAAM,CAAC,mBAAmB,oCAA8B;IACxD,MAAM,CAAC,qBAAqB,sCAAgC;IAC5D,MAAM,CAAC,wBAAwB,yCAAmC;IAElE,MAAM,CAAC,MAAM,wBAAkB;IAC/B,MAAM,CAAC,YAAY,8BAAwB;CAC5C;AAED,eAAO,MACL,SAAS,2BACT,QAAQ,0BACR,kBAAkB,oCAClB,yBAAyB,2CACzB,iBAAiB,mCACjB,aAAa,+BACb,aAAa,+BACb,cAAc,gCACd,eAAe,iCACf,mBAAmB,qCACnB,mBAAmB,qCACnB,qBAAqB,uCACrB,wBAAwB,wCAChB,CAAC;AAEX,MAAM,QAAQ,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AACtC,MAAM,QAAQ,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;AAElD,yBAAiB,IAAI,CAAC;IACpB,MAAM,QAAQ,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;IAEnD,MAAM,QAAQ,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;IAC5C,MAAM,QAAQ,eAAe,GAAG,GAAG,CAAC,eAAe,CAAC;IAEpD,MAAM,QAAQ,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IAE9B,MAAM,QAAQ,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;IAC1C,MAAM,QAAQ,uBAAuB,GAAG,GAAG,CAAC,uBAAuB,CAAC;IACpE,MAAM,QAAQ,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;IACxC,MAAM,QAAQ,qBAAqB,GAAG,GAAG,CAAC,qBAAqB,CAAC;IAEhE,MAAM,QAAQ,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IAEhC,MAAM,QAAQ,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAClC,MAAM,QAAQ,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IAChC,MAAM,QAAQ,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC;IAC9C,MAAM,QAAQ,iBAAiB,GAAG,GAAG,CAAC,iBAAiB,CAAC;IAExD,MAAM,QAAQ,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;IAC5C,MAAM,QAAQ,kBAAkB,GAAG,GAAG,CAAC,kBAAkB,CAAC;IAC1D,MAAM,QAAQ,kBAAkB,GAAG,GAAG,CAAC,kBAAkB,CAAC;CAC3D;AAED,eAAe,IAAI,CAAC"}